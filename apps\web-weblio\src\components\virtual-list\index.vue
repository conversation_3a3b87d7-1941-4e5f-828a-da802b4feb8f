<script setup lang="ts">
// import { debounce } from 'lodash-es'; // 移除
import { computed, ref, watch } from 'vue';

interface Props {
  items: any[];
  itemHeight: number;
  containerHeight: number;
  buffer?: number;
  getKey?: (item: any, index: number) => number | string;
}

const props = withDefaults(defineProps<Props>(), {
  buffer: 5,
  getKey: (item: any, index: number) => index,
});

const containerRef = ref<HTMLElement>();
const scrollTop = ref(0);

// 计算总高度
const totalHeight = computed(() => props.items.length * props.itemHeight);

// 计算容器样式
const containerStyle = computed(() => ({
  height: `${props.containerHeight}px`,
  overflow: 'auto',
  position: 'relative' as const,
}));

// 计算可视区域的起始和结束索引
const startIndex = computed(() => {
  const index = Math.floor(scrollTop.value / props.itemHeight);
  return Math.max(0, index - props.buffer);
});

const endIndex = computed(() => {
  const visibleCount = Math.ceil(props.containerHeight / props.itemHeight);
  const index = startIndex.value + visibleCount + props.buffer * 2;
  return Math.min(props.items.length - 1, index);
});

// 可视区域内的数据
const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value + 1);
});

// 内容区域样式
const contentStyle = computed(() => ({
  position: 'absolute' as const,
  top: `${startIndex.value * props.itemHeight}px`,
  left: '0',
  right: '0',
}));

// 获取项目的key
const getItemKey = (item: any, index: number) => {
  return props.getKey(item, startIndex.value + index);
};

// 获取项目样式
const getItemStyle = (_index: number) => ({
  height: `${props.itemHeight}px`,
  overflow: 'hidden',
});

// 原生setTimeout防抖的滚动处理
let debounceTimer: null | number = null;
const handleScroll = (event: Event) => {
  if (debounceTimer) clearTimeout(debounceTimer);
  debounceTimer = window.setTimeout(() => {
    const target = event.target as HTMLElement;
    scrollTop.value = target.scrollTop;
  }, 16); // 约60fps
};

// 监听items变化，重置滚动位置
watch(
  () => props.items,
  () => {
    scrollTop.value = 0;
    if (containerRef.value) {
      containerRef.value.scrollTop = 0;
    }
  },
);

// 暴露滚动到指定位置的方法
const scrollToIndex = (index: number) => {
  if (containerRef.value) {
    const scrollPosition = index * props.itemHeight;
    containerRef.value.scrollTop = scrollPosition;
    scrollTop.value = scrollPosition;
  }
};

defineExpose({
  scrollToIndex,
});
</script>

<template>
  <div
    ref="containerRef"
    class="virtual-list-container"
    :style="containerStyle"
    @scroll="handleScroll"
  >
    <!-- 占位元素，用于撑开滚动高度 -->
    <div :style="{ height: `${totalHeight}px` }"></div>

    <!-- 可视区域内的元素 -->
    <div class="virtual-list-content" :style="contentStyle">
      <div
        v-for="(item, index) in visibleItems"
        :key="getItemKey(item, index)"
        class="virtual-list-item"
        :style="getItemStyle(index)"
      >
        <slot :item="item" :index="startIndex + index"></slot>
      </div>
    </div>
  </div>
</template>

<style scoped>
.virtual-list-container {
  overflow-y: auto;
  overflow-x: hidden;
}

.virtual-list-content {
  width: 100%;
}

.virtual-list-item {
  box-sizing: border-box;
}

/* 美化滚动条 */
.virtual-list-container::-webkit-scrollbar {
  width: 6px;
}

.virtual-list-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.virtual-list-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.virtual-list-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
