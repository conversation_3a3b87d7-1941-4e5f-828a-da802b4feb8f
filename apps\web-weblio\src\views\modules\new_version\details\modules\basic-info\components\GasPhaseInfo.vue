<script setup lang="ts">
import {
  HeartIcon,
  ShareIcon,
  ViewListIcon,
  ViewModuleIcon,
  CalculatorIcon,
  ChevronLeftIcon
} from 'tdesign-icons-vue-next';
import {
  Button,
  Checkbox,
  CheckboxGroup,
  Empty,
  Loading,
  Pagination,
  Select,
  Table,
  Tag,
  Dialog
} from 'tdesign-vue-next';
import { computed, h, onMounted, reactive, ref } from 'vue';

import {
  getGasPhaseInfo,
  getGasConstantPressureInfo
} from '../api';
import { useSearchStore } from '#/store/search';

interface GasPhaseInfo {
  quantity: string;
  value: string;
  units: string;
  method: string;
  reference: string;
  comment: string;
}

interface GasConstantInfo {
  cpgas: string;
  temperature: string;
  reference: string;
  comment: string;
}
/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
  * 加载状态
  */
  loading: true,

});
// 响应式数据
const gasList = ref<GasPhaseInfo[][]>([]);
const gasConstantList = ref<GasConstantInfo[][]>([]);
// 表格列定义
const getGasColumns = () => [
  {
    colKey: 'quantity',
    title: 'Quantity',
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => {
      // 热力学正则
      const thermoRegex = /([Δ°][a-z]?[A-Z][a-z]?°?|[T|P|V|ρ][A-Z]?°?|$[a-z]$)/g;
      const value = row.quantity?.toString() || '';  // 空值保护

      // 拆分原始文本为匹配段和非匹配段
      const segments: any[] = [];
      let lastIndex = 0;
      let match;

      while ((match = thermoRegex.exec(value)) !== null) {
        // 添加非匹配文本
        if (match.index > lastIndex) {
          segments.push(value.substring(lastIndex, match.index));
        }
        // 添加匹配项
        segments.push(match[0]);
        lastIndex = match.index + match[0].length;
      }
      // 添加剩余文本
      if (lastIndex < value.length) {
        segments.push(value.substring(lastIndex));
      }

      return h('div',
        segments.map(item => {
          // 状态词/数字/相态标注下标
          if (/(\d+|\([a-z]\)|\b(?:gas|liquid|boil|melt|vap|sub|fusion|solid|aq|fus|triple|c)\b)/g.test(item)) {
            return h('sub', item);
          }
          return item;
        })
      );
    }
  },
  {
    colKey: 'value',
    title: 'Value',
    width: 100,
    ellipsis: true,
  },
  {
    colKey: 'units',
    title: 'Units',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'method',
    title: 'Method',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 150,
    ellipsis: true,
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 400,
    ellipsis: true,
  },
];

const getGasConstantColumns = () => [
  {
    colKey: 'id',
    title: 'ID',
  },
  {
    colKey: 'cpgas',
    title: (h) => h('div', {
      style: 'display: grid; gap: 2px;'
    }, [
      h('div', ['C', h('sub', 'p,gas')]),
      h('div', '(J/mol·K)')
    ]),
    width: 150,
    ellipsis: true,
  },
  {
    colKey: 'temperature',
    title: (h) => h('div', {
      style: 'display: grid; gap: 2px;'
    }, [
      h('div', 'Temperature'),
      h('div', '(K)')
    ]),
    width: 150,
    ellipsis: true,
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 300,
    ellipsis: true,
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 500,
    ellipsis: true,
  }
];

// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    console.log("当前详情数据", detail, 2222);
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    console.log("当前详情数据", local, 2222);
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

const displayColumns = ref(['cpgas', 'temperature', 'reference', 'comment']);
const getGasConstantRowspanAndColspan = ({ col, rowIndex }) => {
  if (col.colKey === 'reference' || col.colKey === 'comment') {
    console.log(rowIndex, col)
    if (rowIndex > 0) {
      return {
        rowspan: 0,
        colspan: 0
      }
    }
    return { rowspan: gasConstantList.value.length, colspan: 1 }
  }
  return { rowspan: 1, colspan: 1 };
}
onMounted(async () => {
  try {
    // 调用气相信息查询API
    const response = await getGasPhaseInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    gasList.value = response;
    // 调用气相相变液体恒压热容信息查询API
    const res = await getGasConstantPressureInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    gasConstantList.value = res;
  } catch (error) {
    console.error('获取气相信息失败:', error);
  } finally {
    // 关闭加载状态
    state.loading = false;
  }
});
</script>

<template>
  <div class="gas-phase-info">
    <h2>Gas phase thermochemistry data</h2>
    <div v-for="gas in gasList">
      <Table :data="gas" :columns="getGasColumns()" :bordered="true" :hover="true" :stripe="true" row-key="quantity"
        table-layout="fixed" cell-empty-content="-" />
    </div>
    <br />
    <h2>Constant pressure heat capacity of gas</h2>
    <div v-for="gasConstant in gasConstantList">
      <Table :data="gasConstant" :columns="getGasConstantColumns()" :displayColumns="displayColumns" :bordered="true"
        :hover="true" :stripe="true" row-key="id" :rowspan-and-colspan="getGasConstantRowspanAndColspan"
        table-layout="fixed" cell-empty-content="-" />
    </div>

  </div>
</template>

<style scoped lang="scss">
.gas-phase-info {
  h2 {
    font-size: 20px;
    margin-bottom: 10px;
  }
}
</style>
