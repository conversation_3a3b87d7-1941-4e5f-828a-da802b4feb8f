<script setup lang="ts">
import {
  HeartIcon,
  ShareIcon,
  ViewListIcon,
  ViewModuleIcon,
  CalculatorIcon,
  ChevronLeftIcon
} from 'tdesign-icons-vue-next';
import {
  Button,
  Checkbox,
  CheckboxGroup,
  Empty,
  Loading,
  Pagination,
  Select,
  Table,
  Tag,
  Dialog
} from 'tdesign-vue-next';
import { computed, h, onMounted, reactive, ref } from 'vue';

import {
  getPhaseChangeInfo,
  getEnthalpyOfVaporizationInfo,
  getEnthalpyParametersInfo
} from '../api';
import { useSearchStore } from '#/store/search';

interface PhaseChangeInfo {
  quantity: string;
  value: string;
  units: string;
  method: string;
  reference: string;
  comment: string;
}

interface EnthalpyInfo {
  cpgas: string;
  temperature: string;
  reference: string;
  comment: string;
}

interface EnthalpyParametersInfo {
  formula: string;
  temperature: string;
  a: string;
  alpha: string;
  beta: string;
  tc: string;
  reference: string;
}

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
  * 加载状态
  */
  loading: true,

});
// 响应式数据
const phaseList = ref<PhaseChangeInfo[][]>([]);
const enthalpyList = ref<EnthalpyInfo[]>([]);
const enthalpyParameters = ref<any>([]);

// 表格列定义
const getPhaseChangeColumns = () => [
  {
    colKey: 'quantity',
    title: 'Quantity',
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => {
      // 热力学正则
      const thermoRegex = /([Δ°][a-z]?[A-Z][a-z]?°?|[T|P|V|ρ][A-Z]?°?|$[a-z]$)/g;
      const value = row.quantity?.toString() || '';  // 空值保护

      // 拆分原始文本为匹配段和非匹配段
      const segments: any[] = [];
      let lastIndex = 0;
      let match;

      while ((match = thermoRegex.exec(value)) !== null) {
        // 添加非匹配文本
        if (match.index > lastIndex) {
          segments.push(value.substring(lastIndex, match.index));
        }
        // 添加匹配项
        segments.push(match[0]);
        lastIndex = match.index + match[0].length;
      }
      // 添加剩余文本
      if (lastIndex < value.length) {
        segments.push(value.substring(lastIndex));
      }

      return h('div',
        segments.map(item => {
          // 状态词/数字/相态标注下标
          if (/(\d+|\([a-z]\)|\b(?:gas|liquid|boil|melt|vap|sub|fusion|solid|aq|fus|triple|c)\b)/g.test(item)) {
            return h('sub', item);
          }
          return item;
        })
      );
    }
  },
  {
    colKey: 'value',
    title: 'Value',
    width: 100,
    ellipsis: true,
  },
  {
    colKey: 'units',
    title: 'Units',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'method',
    title: 'Method',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 150,
    ellipsis: true,
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 400,
    ellipsis: true,
  },
];

const getEnthalpyColumns = () => [
  {
    colKey: 'id',
    title: 'ID',
  },
  {
    colKey: 'vaph',
    title: (h) => h('div', [
      h('div', ['Δ', h('sub', 'vap'),
        'H (kJ/mol)'])
    ]),
    width: 150,
    ellipsis: true,
  },
  {
    colKey: 'temperature',
    title: (h) => h('div', [
      h('div', 'Temperature(K)')
    ]),
    width: 150,
    ellipsis: true,
  },
  {
    colKey: 'method',
    title: 'Method',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 300,
    ellipsis: true,
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 500,
    ellipsis: true,
  }
];

// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    console.log("当前详情数据", detail, 2222);
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    console.log("当前详情数据", local, 2222);
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

const displayColumns = ref(['vaph', 'temperature', 'method', 'reference', 'comment']);
onMounted(async () => {
  try {
    // 调用相变信息查询API
    const response = await getPhaseChangeInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    phaseList.value = response;
    // 调用相变汽化焓信息查询API
    const res = await getEnthalpyOfVaporizationInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    enthalpyList.value = res;
    // 调用相变汽化焓信息查询API
    const resData = await getEnthalpyParametersInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    enthalpyParameters.value = formattedEnthalpyParameters(resData);
  } catch (error) {
    console.error('获取相变信息失败:', error);
  } finally {
    // 关闭加载状态
    state.loading = false;
  }
});
function formattedEnthalpyParameters(data: any) {
  return [
    { param: 'Temperature (K)', value: data.temperature },
    { param: 'A (kJ/mol)', value: data.a },
    { param: 'α', value: data.alpha },
    { param: 'β', value: data.beta },
    { param: 'Tc (K)', value: data.tc },
    { param: 'Reference', value: data.reference }
  ];
}
</script>

<template>
  <div class="phase-info">
    <h2>Phase change data</h2>
    <div v-for="phase in phaseList">
      <Table :data="phase" :columns="getPhaseChangeColumns()" :bordered="true" :hover="true" :stripe="true"
        row-key="quantity" table-layout="fixed" cell-empty-content="-" />
    </div>
    <br />
    <h2>Enthalpy of vaporization</h2>
    <Table :data="enthalpyList" :columns="getEnthalpyColumns()" :displayColumns="displayColumns" :bordered="true"
      :hover="true" :stripe="true" row-key="id" table-layout="fixed" cell-empty-content="-" />
    <br />
    <h2>Enthalpy of vaporization</h2>
    <div class="enthalpy-parameters">
      <div class="row" v-for="(item, index) in enthalpyParameters" :key="index">
        <div class="param" v-if="item.param !== 'Tc (K)'">
          {{ item.param }}
        </div>
        <div class="param" v-else>
          T<sub>c</sub>(K)
        </div>
        <div class="value" >
          {{ item.value }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.phase-info {
  h2 {
    font-size: 20px;
    margin-bottom: 10px;
  }

  .enthalpy-parameters {
    display: table;
    width: 50%;
    border-collapse: collapse;

    .row {
      display: table-row;

      .param,
      .value {
        display: table-cell;
        padding: 8px;
        border: 1px solid #ddd;
        text-align: center;
      }

      .param {
        background-color: #ffebee;
        font-weight: bold;
        text-align: left;
      }

      .value {
        background-color: #fff;
        text-align: right;
      }
    }
  }
}
</style>
