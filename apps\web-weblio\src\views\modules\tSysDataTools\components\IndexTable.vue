<script setup lang="ts">
import { getDictItems } from '#/api';
import { defineProps, onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';
import {
  AddCircleIcon,
  DeleteIcon,
  RefreshIcon,
  SearchIcon,
  DownloadIcon,
  ChevronDownDoubleIcon
} from 'tdesign-icons-vue-next';

import {
  Button,
  Pagination,
  type PageInfo,
  Space,
} from 'tdesign-vue-next';
import { listByPage } from '../api';

/**
 * 属性定义
 */
const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },
  /**
   * 编辑表单组件动作句柄
   */
  editFormRef: { type: Object, default: null },
  /**
   * 外部扩展查询字段
   */
  extendSearchObj: {
    type: Object,
    default: () => { },
  },
});

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * reload触发时二次传入数据
   */
  tagObj: {},
  /**
   * 被选中key
   */
  selectedRowKeys: [],
  /**
   * 删除提示显示标志
   */
  delDailogShow: false,
  /**
   * 数据元
   */
  dataSource: [],
  /**
   * 查询展开/关闭多字段
   */
  hideQuery: false,
  /**
   * 加载状态
   */
  loading: false,
  /**
   * table排序
   */
  sort: [],
});

/**
 * 分页参数
 */
const Paginations = {
  current: 1,
  pageSize: 20,
  total: 0,
};
const pagination: any = ref(Paginations);
const name = ref('');
const sortOption = ref('create_time');
// 添加排序状态
const isDescending = ref(true); // 默认降序

/**
 * 网络访问方法定义
 */
const reqRunner = {
  /**
   * 分页获得列表数据
   */
  listByPage: useRequest(listByPage, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => { },
    onSuccess: (res: any) => {
      const { records, total } = res;
      state.dataSource = records;
      pagination.value = {
        ...pagination.value,
        total,
      };

      if (records.length === 0 && total !== 0) {
        pagination.value.current = 1;
        reload();
      }
    },
  }),
};

/**
 * 重新加载数据
 * @param data
 */
function reload(data?: any) {
  if (data) {
    state.tagObj = data;
  }
  const { run, loading } = reqRunner.listByPage;
  state.loading = loading;
  run({
    param: { name: name.value, toolsType: selectedCategory.value },
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sort: {
      // 排序字段
      sortBy: sortOption.value || 'create_time',
      // 排序方式
      descending: isDescending.value
    }
  });
};

/**
 * 查询Form提交，执行查询
 */
const handleSearch = () => {
  // 重置分页
  pagination.value = {
    current: 1,
    pageSize: pagination.value.pageSize,
  };
  setTimeout(() => {
    reload();
  }, 0);

};

/**
 * table分页变化时调用
 * @param pageInfo
 */
const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  reload();
};

// 排序方法
const sortChange = () => {
  isDescending.value = !isDescending.value;
  reload();
};

const handleSortChange = (event: any) => {
  sortOption.value = event.target.selectedOptions[0].value;
};

/**
 * 工具分类数据
 */
const toolCategories = ref<Array<{
  label: string;
  value: string;
}>>([])

const selectedCategory = ref('');

const toggleCategory = (categoryValue) => {
  if (selectedCategory.value === categoryValue) {
    // 如果点击的是已选中的分类，则取消选中
    selectedCategory.value = '';
    reload();
  } else {
    // 否则选中新的分类
    selectedCategory.value = categoryValue;
    getCategoryData(categoryValue); // 保持原有数据获取逻辑
  }
};

/**
 * 根据分类筛选数据
 */
function getCategoryData(value: any) {
  selectedCategory.value = value;
  reload();
}


onMounted(async () => {
  toolCategories.value = await getDictItems('DATA_TOOLS_CLASSIFY');
  reload();
});

/**
 * 导出资源
 */
defineExpose({
  reload,
});
</script>

<template>
  <Space :size="8" class="w-full tiny-tdesign-style-patch" direction="vertical">
    <div class="container-main">
      <!-- 左侧工具分类区域 -->
      <aside class="category-sidebar">
        <h3 class="sidebar-title">工具分类</h3>
        <ul class="category-list">
          <li v-for="category in toolCategories" :key="category.label" class="category-item"
            :class="{ active: selectedCategory === category.value }" @click="toggleCategory(category.value)">
            {{ category.label }}
          </li>
        </ul>
      </aside>
      <div class="content-data">
        <!-- 查询表单定义区域 -->
        <div class="search-input-container">
          <!-- 搜索输入框 -->
          <input v-model="name" type="text" class="search-input" placeholder="数据工具名称查询" @keyup.enter="handleSearch" />
          <!-- 搜索按钮 -->
          <button class="search-btn" @click="handleSearch">检索</button>
        </div>
        <!-- 结果数量提示 -->
        <div class="flex justify-between items-center mb-4">
          <div class="text-gray-500">
            搜索到 {{ state.dataSource.length || 0 }} 个结果
          </div>
          <ChevronDownDoubleIcon :class="['swap-style', { 'rotate-180': !isDescending }]" @click="sortChange" />
          <Select v-model="sortOption" class="w-40" @change="handleSortChange">
            <Option value="create_time">最新发布</Option>
            <Option value="name">数据工具名称</Option>
          </Select>
        </div>

        <!-- 卡片展示区域 -->
        <div class="card-container">
          <div v-for="item in state.dataSource" :key="item.id" class="tool-card">
            <div class="card-header">
              <div class="tool-info">
                <img :src="item.img" class="tool-icon" />
                <h3>{{ item.name }}</h3>
                <p class="version">{{ item.version }}</p>
                <p>{{ item.describe }}</p>
                <p>{{ item.classificationName }}</p>
              </div>
            </div>
          </div>
        </div>
        <!-- 分页 -->
        <Pagination v-model:current="pagination.current" :total="pagination.total" :pageSize="pagination.pageSize"
          @change="rehandlePageChange" class="mt-4" v-if="pagination.total > 0" />
      </div>
    </div>
  </Space>
</template>
<style scoped>
.t-form__item {
  margin-bottom: 0;
}

.container-main {
  display: flex;
}

.category-sidebar {
  width: 200px;
  padding: 16px;
}

.sidebar-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #374151;
}

.category-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-item {
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 4px;
  cursor: pointer;
  color: #4b5563;
  transition: all 0.2s ease;
}

.category-item:hover {
  background-color: #edf2f7;
  color: #3182ce;
}

.category-item.active {
  background-color: #3182ce;
  color: white;
}

.content-data {
  flex: 1;
  padding: 16px;
  margin-top: 20px;
}

.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.tool-card {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.card-header {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tool-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.tool-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

=.tool-icon {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  object-fit: contain;
}

.search-input-container {
  position: relative;
  display: flex;
  width: 50%;
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  padding: 12px 20px;
  font-size: 14px;
  border: 1px solid #3182ce;
  border-radius: 24px;
  outline: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  &::placeholder {
    color: #9ca3af;
    font-size: 14px;
  }
}

.search-btn {
  position: absolute;
  position: absolute;
  top: 3px;
  right: 5px;
  padding: 10px 20px;
  border: none;
  border-radius: 24px;
  font-size: 14px;
  font-weight: 600;
  background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
  color: #fff;
  cursor: pointer;

  &:hover {
    background: linear-gradient(135deg, #2c5aa0 0%, #2a4d96 100%);
  }
}

.w-40 {
  border: 1px solid;
}

.version {
  color: #6b7280;
  font-size: 14px;
}

.swap-style {
  margin-left: auto;
  margin-right: 10px;
  font-size: 18px;
  cursor: pointer;
}

.swap-style.rotate-180 {
  transform: rotate(180deg);
}

.card-content {
  color: #4b5563;
  font-size: 14px;
  line-height: 1.5;
}
</style>
