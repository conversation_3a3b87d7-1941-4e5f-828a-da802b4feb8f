import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any>('/rgdc-user/account/listByPage', data);
}

export async function modelListByPage(data: any, deptId: any) {
  return requestClient.post<any>(
    `/rgdc-user/account/modelListByPage?secondParam=${deptId}`,
    data,
  );
}

export async function save(data: any) {
  return requestClient.post<any>('/rgdc-user/account/save', data);
}

// 批量删除
export async function deleteBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-user/account/deleteBatch/${data}`);
}

export async function getOne(data: any) {
  return requestClient.get<any>(`/rgdc-user/account/getOne/${data}`);
}

export async function getByIds(data: any) {
  return requestClient.get<any>(`/rgdc-user/account/getByIds/${data}`);
}

export async function updatePassword(data: any) {
  return requestClient.post<any>(`/rgdc-user/account/updatePassword`, data);
}
export async function allAccountList() {
  return requestClient.get<any>('/rgdc-user/account/allList');
}

// 分页获取用户信息
export async function getAccountPage(data: any) {
  console.log('data', data);
  return requestClient.post<any>('/rgdc-user/account/getAccountPage', data);
}

// 获取部门列表
export async function getDeptList(data: any) {
  return requestClient.post<any>('/rgdc-user/account/getDeptList', data);
}

// 保存扩展信息
export async function saveAccountExtension(data: any) {
  return requestClient.post<any>('/rgdc-user/account/saveAccountExtension', data);
}

// 批量删除
export async function deleteAccountBatch(data: any) {
  return requestClient.delete<any>(`/rgdc-user/account/deleteAccountBatch/${data}`);
}
