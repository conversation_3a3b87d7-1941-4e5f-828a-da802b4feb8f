<script setup lang="tsx">
import type { TreeNodeValue } from 'tdesign-vue-next';

import { getDictItems } from '#/api';
import Collection from '#/components/collection/index.vue';
import SearchBox from '#/components/search-box/index.vue';
import Share from '#/components/share/index.vue';
import { useSearchStore } from '#/store/search';
import EditForm from '#/views/modules/tSearch/components/EditForm.vue';
import { useUserStore } from '@vben/stores';
import { ViewListIcon, ViewModuleIcon } from 'tdesign-icons-vue-next';
import {
  Checkbox,
  CheckboxGroup,
  Dialog,
  Empty,
  Loading,
  MessagePlugin,
  Pagination,
  RadioGroup,
  Select,
  Table,
  Tag,
  Tree,
} from 'tdesign-vue-next';
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import {
  getCollectByClassCode,
  getLabelManage,
  getResourcesTypeWithCount,
  getSearchData,
} from './api';

// 定义 ListItem 类型，确保 listData 类型安全
interface ListItem {
  id: number | string;
  infomation: {
    author?: string;
    browsingCount?: number;
    data_name: string;
    describe?: string;
    img: string;
  };
  createTime: string;
  isFavorite: boolean;
  tags: string[];
  labelName?: string;
  dataType_text?: string;
  dataAuth_text?: string;
  dataType?: number | string;
  dataAuth?: number | string;
  isVisible?: number; // 审批状态：1-审批通过，0-待审批
}
const editFormRef = ref();
const searchStore = useSearchStore();
const userStore = useUserStore();
const router = useRouter();
const columns = ref<any[]>([
  {
    colKey: 'data_name',
    title: '名称',
  },
  {
    colKey: 'dataType',
    title: '资源类型',
  },
  {
    colKey: 'dataAuth',
    title: '共享方式',
  },
  {
    colKey: 'createTime',
    title: '发布时间',
  },
  {
    colKey: 'categoryName',
    title: '所属分类',
  },
  {
    colKey: 'actions',
    title: '操作',
  },
]);
const viewType = ref<'card' | 'list'>('card');
// 右侧条件区数据（提前声明，指定类型）
const resourceTypeOptions = ref<
  {
    count?: number;
    label: string;
    text?: string;
    value: string;
  }[]
>([]); // 资源类型
const shareTypeOptions = ref<
  { checkAll?: boolean; label: string; value: string }[]
>([]); // 共享方式
const yearOptions = ref<{ label: string; value: string }[]>([]); // 年份
const keywordOptions = ref<
  { count?: number; label: string; theme?: string; value: string }[]
>([]); // 关键词

const selectedResourceTypes = ref<string[]>(
  searchStore.selectedSearchType ? [searchStore.selectedSearchType] : [],
); // 资源类型
const selectedShareTypes = ref<string[]>(['']); // 共享方式
const selectedYears = ref<string[]>([]); // 年份
const selectedKeywords = ref<string[]>([]); // 关键词

// 选中的树节点
const checkedKeys = ref<TreeNodeValue[]>([]);
// 展开的树节点
const expandedKeys = ref<TreeNodeValue[]>([]);

// 搜索相关数据
const listData = ref<ListItem[]>([]);
const total = ref(0);
const pageSize = ref(20);
const currentPage = ref(1);
const sortType = ref<'count' | 'date' | 'default'>('default');
const loading = ref(false);
const treeLoading = ref(false);

// 执行搜索（静态结果，之后替换成API）
const performSearch = async () => {
  loading.value = true;
  const params = {
    current: currentPage.value,
    pageSize: pageSize.value,
    category: checkedKeys.value, // 分类
    searchContent: searchStore.searchQuery, // 搜索内容
    queryType: selectedResourceTypes.value.filter((el) => el !== 'all'), // 资源类型
    dataAuthList: selectedShareTypes.value.filter((el) => !!el), // 共享
    years: selectedYears.value.filter((el) => !el.includes('_before')), // 年份
    yearBefore: selectedYears.value.filter((el) => el.includes('_before')), // 年份
    labelCode: selectedKeywords.value?.[0] || '', // 关键词
    esIndex: import.meta.env.VITE_ES_INDEX, // 索引
    sortType: sortType.value,
    logicList: searchStore.logicList,
  };
  try {
    const res = await getSearchData(params);
    if (res) {
      listData.value = res.page.records;
      total.value = res.page.total;
      // 重写关键词
      keywordOptions.value = res.buckets;
    }
    loading.value = false;
  } catch (error) {
    console.error('搜索失败', error);
  } finally {
    loading.value = false;
  }
};

// 搜索框数据
// const searchMode = computed({
//   get: () => searchStore.searchMode,
//   set: (v) => searchStore.setSearchMode(v),
// });
// const selectedSearchType = computed({
//   get: () => searchStore.selectedSearchType,
//   set: (v) => searchStore.setSelectedSearchType(v),
// });
// const searchQuery = computed({
//   get: () => searchStore.searchQuery,
//   set: (v) => searchStore.setSearchQuery(v),
// });
// const aiQuery = computed({
//   get: () => searchStore.aiQuery,
//   set: (v) => searchStore.setAiQuery(v),
// });
// const deepSearchEnabled = computed({
//   get: () => searchStore.deepSearchEnabled,
//   set: (v) => searchStore.setDeepSearchEnabled(v),
// });

// AI问答示例
const aiExamples = ref([
  '什么是苯的分子结构？',
  '如何合成阿司匹林？',
  '解释一下化学反应机理',
  '有机化学基础知识',
]);

// 左侧树形结构数据
const treeData = ref();

// 递归查找目标节点，并收集其所有子节点的 classifyCode
function findAllClassifyCodes(tree: any[], targetCode: string): string[] {
  // 递归查找目标节点
  function findNode(nodes: any[]): any | null {
    for (const node of nodes) {
      if (`${node.classifyCode}` === targetCode) {
        return node;
      }
      if (node.children) {
        const found = findNode(node.children);
        if (found) return found;
      }
    }
    return null;
  }

  // 递归收集所有 classifyCode
  function collectCodes(node: any): string[] {
    let codes = [node.classifyCode];
    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        codes = [...codes, ...collectCodes(child)];
      }
    }
    return codes;
  }

  const targetNode = findNode(tree);
  if (!targetNode) return [];
  return collectCodes(targetNode);
}

// 获取树的前两层节点的keys
function getFirstTwoLevelsKeys(tree: any[], currentLevel = 1): string[] {
  if (!tree || currentLevel > 2) return [];

  const keys: string[] = [];
  for (const node of tree) {
    if (currentLevel < 2) {
      keys.push(node.classifyCode);
      if (node.children && node.children.length > 0) {
        keys.push(...getFirstTwoLevelsKeys(node.children, currentLevel + 1));
      }
    }
  }
  return keys;
}

// 初始化左侧树结构数据
const initTreeData = async () => {
  treeLoading.value = true;
  try {
    // 这里 params 可根据实际需求调整
    const params = {};
    const res = await getCollectByClassCode(params);
    if (res && Array.isArray(res)) {
      treeData.value = res;

      // 设置默认展开前两层节点
      try {
        expandedKeys.value = getFirstTwoLevelsKeys(treeData.value);
      } catch (expandError) {
        console.error('设置展开节点失败:', expandError);
        expandedKeys.value = [];
      }

      // 处理初始分类选择
      if (searchStore.initCategory !== undefined) {
        try {
          // 递归查找并收集所有节点的 classifyCode
          checkedKeys.value = findAllClassifyCodes(
            treeData.value,
            String(searchStore.initCategory),
          );
        } catch (categoryError) {
          console.error('设置初始分类失败:', categoryError);
          checkedKeys.value = [];
        }
      }
    } else {
      console.warn('获取到的树结构数据格式不正确');
      treeData.value = [];
    }
  } catch (error) {
    console.error('获取左侧树结构失败:', error);
    // 设置默认值确保页面正常工作
    treeData.value = [];
    expandedKeys.value = [];
    checkedKeys.value = [];
  } finally {
    treeLoading.value = false;
  }
};
// 初始化字典数据
const initDictData = async () => {
  try {
    // 共享方式
    try {
      const sharingMethod = await getDictItems('SHARING_METHOD');
      shareTypeOptions.value = [
        // { label: '全选', value: '', checkAll: true },
        ...sharingMethod,
      ];
    } catch (error) {
      console.error('获取共享方式字典失败:', error);
      // 设置默认值确保页面正常工作
      shareTypeOptions.value = [{ label: '全选', value: '', checkAll: true }];
    }

    // 资源类型
    try {
      const res = await getResourcesTypeWithCount({});
      resourceTypeOptions.value = res || [];
    } catch (error) {
      console.error('获取资源类型失败:', error);
      // 设置默认值确保页面正常工作
      resourceTypeOptions.value = [];
    }

    // 关键词
    try {
      const labelManage = await getLabelManage({});
      keywordOptions.value = labelManage || [];
    } catch (error) {
      console.error('获取关键词标签失败:', error);
      // 设置默认值确保页面正常工作
      keywordOptions.value = [];
    }
  } catch (error) {
    console.error('初始化字典数据时发生未知错误:', error);
  }
};
// 初始化年份
const initYearOptions = () => {
  const currentYear = new Date().getFullYear();
  const years: { label: string; value: string }[] = [];
  for (let i = 0; i < 5; i++) {
    const y = (currentYear - i).toString();
    years.push({ label: `${y}年`, value: y });
  }
  years.push({
    label: `${currentYear - 5}年及更早`,
    value: `${currentYear - 5}_before`,
  });
  yearOptions.value = years;
};

// 统一检索入口
function handleSearch() {
  currentPage.value = 1;
  performSearch();
}

// 资源类型（树）变化
function handleResourceTypeChange() {
  currentPage.value = 1;
  performSearch();
}

// 排序变化
function handleSortChange() {
  currentPage.value = 1;
  performSearch();
}

// 分页变化
function handlePageChange(pageInfo: { current: number; pageSize: number }) {
  currentPage.value = pageInfo.current;
  pageSize.value = pageInfo.pageSize;
  performSearch();
}

function handlePageSizeChange(newPageSize: number) {
  pageSize.value = newPageSize;
  currentPage.value = 1;
  performSearch();
}

// 关键词点击
function _handleKeywordClick(_val: string) {
  selectedKeywords.value =
    selectedKeywords.value.length === 1 && selectedKeywords.value[0] === _val
      ? []
      : [_val];
  currentPage.value = 1;
  performSearch();
}

const sortOptions = ref([
  { label: '默认排序', value: 'default' },
  { label: '发布时间', value: 'date' },
  { label: '热度', value: 'count' },
]);

const treeFilter = ref('');

// 递归过滤树节点
function filterTree(data: any[], keyword: string) {
  if (!keyword) return data;
  return data
    .map((node) => {
      const children = node.children ? filterTree(node.children, keyword) : [];
      if (
        (node.classifyName && node.classifyName.includes(keyword)) ||
        (children && children.length > 0)
      ) {
        return { ...node, children };
      }
      return null;
    })
    .filter(Boolean);
}

const filteredTreeData = computed(() =>
  filterTree(treeData.value || [], treeFilter.value.trim()),
);

// 其它操作（收藏、分享、点击项）
function handleItemClick(item: any) {
  // 处理点击项逻辑，避免row属性的问题
  const targetItem = item?.row || item;

  // 从用户信息中获取sourceSystemCode，需要进行类型安全检查
  const sourceSystemCode = (userStore.userInfo as any)?.sourceSystemCode;
  const { dataAuth, isVisible } = targetItem;

  // 统一数据类型处理
  const authCode = dataAuth && String(dataAuth);
  const visibleStatus = Number(isVisible);
  if (!authCode) {
    MessagePlugin.warning('该资源暂不共享，请选择其他资源。');
    return;
  }
  // 条件判断逻辑
  if (sourceSystemCode === '01' || sourceSystemCode === '02') {
    // 内部用户（管理员或所内用户）
    if (authCode === '0') {
      // 完全共享，直接跳转
      navigateToDetail(targetItem);
    } else if (visibleStatus === 1) {
      // 审批通过，可以跳转
      navigateToDetail(targetItem);
    } else if (visibleStatus === 0) {
      // 待审批状态
      if (authCode === '1') {
        // 审批共享，显示审批弹窗
        showApprovalModal(targetItem);
      } else if (authCode === '2') {
        // 暂不共享，不跳转
        MessagePlugin.warning('该资源暂不共享，请选择其他资源。');
      }
    } else if (authCode === '2') {
      // 其他情况下的暂不共享
      MessagePlugin.warning('该资源暂不共享，请选择其他资源。');
    }
  } else {
    // 外部用户
    // 首先检查是否为暂不共享
    if (authCode === '2') {
      MessagePlugin.warning('该资源暂不共享，请选择其他资源。');
      return;
    }

    if (visibleStatus === 1) {
      // 审批通过，可以跳转
      navigateToDetail(targetItem);
    } else if (visibleStatus === 0) {
      // 待审批状态，显示审批弹窗
      showApprovalModal(targetItem);
    }
  }
}

// 跳转到详情页
function navigateToDetail(item: ListItem) {
  // 存储 item 到 pinia
  searchStore.setCurrentDetailItem(item);
  // 跳转到详情页
  router.push({ path: '/details' });
}

// 显示审批弹窗
function showApprovalModal(item: ListItem) {
  editFormRef.value.open(item);
}

// 处理审批操作
function handleApproval() {
  // TODO: 实现实际的审批逻辑
  console.warn('审批功能待实现');

  // 更新当前item的审批状态
  if (currentApprovalItem.value) {
    currentApprovalItem.value.isVisible = approvalStatus.value;
  }

  // 模拟审批成功
  showApprovalDialog.value = false;

  // 如果审批通过，跳转到详情页
  if (currentApprovalItem.value && approvalStatus.value === 1) {
    navigateToDetail(currentApprovalItem.value);
  }
}

// 关闭审批弹窗
function closeApprovalModal() {
  showApprovalDialog.value = false;
  currentApprovalItem.value = null;
  approvalStatus.value = 0;
}

// 计算属性：根据共享状态获取自定义颜色
const getDataAuthColor = (dataAuth: number | string | undefined) => {
  if (dataAuth === undefined || dataAuth === null) return '#ef4444'; // 默认红色

  switch (+dataAuth) {
    case 0: {
      // 完全共享
      return '#10b981'; // 绿色
    }
    case 1: {
      // 审批共享
      return '#f59e0b'; // 橙色
    }
    case 2: {
      // 暂不共享
      return '#ef4444'; // 红色
    }
    default: {
      return '#6366f1'; // 紫色
    }
  }
};

// 计算属性：根据资源类型获取自定义颜色
const getDataTypeColor = (dataType: number | string | undefined) => {
  if (dataType === undefined || dataType === null) return '#6366f1'; // 默认紫色
  const typeValue =
    typeof dataType === 'string' ? Number.parseInt(dataType) : dataType;

  const colors = [
    '#6366f1', // 紫色
    '#06b6d4', // 青色
    '#10b981', // 绿色
    '#f59e0b', // 橙色
    '#ef4444', // 红色
    '#8b5cf6', // 紫罗兰
    '#ec4899', // 粉色
    '#84cc16', // 石灰绿
    '#f97316', // 橙红色
    '#3b82f6', // 蓝色
  ];

  return colors[typeValue % colors.length];
};

// 计算属性：年份是否禁用
const isYearDisabled = computed(() => {
  const validTypes = new Set(['4', '5', '6', '7']);
  const filteredTypes = selectedResourceTypes.value.filter(
    (el) => el !== 'all',
  );

  // 如果没有选择任何类型，或者有不在允许范围内的类型，则禁用
  return (
    filteredTypes.length === 0 ||
    !filteredTypes.every((type) => validTypes.has(type))
  );
});

// 验证并返回有效的theme值
const getValidTheme = (
  theme: string | undefined,
): 'danger' | 'default' | 'primary' | 'success' | 'warning' => {
  const validThemes = ['primary', 'success', 'warning', 'danger', 'default'];
  return validThemes.includes(theme || '')
    ? (theme as 'danger' | 'default' | 'primary' | 'success' | 'warning')
    : 'primary';
};

// AI相关（如需保留）
function handleAiChat() {
  // TODO: 实现AI对话
}
function handleAdvancedSearch() {
  // TODO: 实现高级检索
}
function handleFormulaSearch() {
  // TODO: 实现公式检索
}

// 共享方式全选逻辑
function handleShareTypeChange(_val: string[]) {
  // 触发搜索
  currentPage.value = 1;
  performSearch();
}

function onCollectionChange(
  row: any,
  event: { isFavorite: boolean; row: any },
) {
  row.isFavorite = event.isFavorite;
}

// 组件挂载时初始化
onMounted(async () => {
  try {
    // 并行执行初始化方法，每个方法内部都有完善的错误处理
    await Promise.all([
      initTreeData(), // 初始化左侧树结构
      initDictData(), // 初始化字典数据
    ]);

    // 初始化年份（同步方法）
    initYearOptions();

    // 所有初始化完成后执行搜索
    await performSearch();
  } catch (error) {
    console.error('初始化过程中发生错误:', error);
    // 即使初始化失败，也要确保执行搜索
    try {
      await performSearch();
    } catch (searchError) {
      console.error('搜索执行失败:', searchError);
    }
  }
});

onUnmounted(() => {
  // 页面销毁时重置searchStore内容
  searchStore.reset();
});
</script>

<template>
  <div class="search-page-container">
    <EditForm ref="editFormRef" @success="performSearch" />
    <!-- 全屏 Loading 蒙版 -->
    <Transition name="loading-fade">
      <div v-if="loading || treeLoading" class="loading-overlay">
        <div class="loading-content">
          <Loading
            :text="loading ? '搜索中...' : '加载分类数据中...'"
            size="large"
          />
        </div>
      </div>
    </Transition>

    <!-- 顶部搜索框 -->
    <SearchBox
      v-model:search-mode="searchStore.searchMode"
      v-model:selected-search-type="searchStore.selectedSearchType"
      v-model:search-query="searchStore.searchQuery"
      v-model:ai-query="searchStore.aiQuery"
      v-model:deep-search-enabled="searchStore.deepSearchEnabled"
      :ai-examples="aiExamples"
      class="search-box-top"
      @search="handleSearch"
      @ai-chat="handleAiChat"
      @advanced-search="handleAdvancedSearch"
      @formula-search="handleFormulaSearch"
    />

    <div class="main-content">
      <!-- 左侧树形结构 -->
      <div class="left-tree">
        <input
          v-model="treeFilter"
          placeholder="检索分类/节点"
          class="tree-filter-input"
        />
        <Loading v-if="treeLoading" text="加载中..." style="margin-top: 32px" />
        <Tree
          v-else
          v-model="checkedKeys"
          v-model:expanded="expandedKeys"
          :data="filteredTreeData"
          :keys="{
            label: 'classifyName',
            value: 'classifyCode',
          }"
          checkable
          @change="handleResourceTypeChange"
        />
      </div>

      <!-- 右侧内容区 -->
      <div class="right-content">
        <!-- 条件区 -->
        <div class="condition-bar">
          <div class="condition-row">
            <span class="condition-label">资源类型</span>
            <CheckboxGroup
              v-model="selectedResourceTypes"
              class="condition-group resource-type-checkbox-group"
              @change="handleResourceTypeChange"
            >
              <Checkbox
                v-for="item in resourceTypeOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ item.text }}（{{ item.count }}）
              </Checkbox>
            </CheckboxGroup>
          </div>
          <div class="condition-row">
            <span class="condition-label">共享方式</span>
            <CheckboxGroup
              v-model="selectedShareTypes"
              class="condition-group"
              @change="handleShareTypeChange"
            >
              <Checkbox
                v-for="item in shareTypeOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </Checkbox>
            </CheckboxGroup>
          </div>
          <div class="condition-row">
            <span class="condition-label">年份</span>

            <CheckboxGroup
              v-model="selectedYears"
              class="condition-group"
              :disabled="isYearDisabled"
            >
              <Checkbox
                v-for="item in yearOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </Checkbox>
            </CheckboxGroup>
          </div>
          <div class="condition-row">
            <span class="condition-label">关键词</span>
            <div class="keyword-tags" v-if="keywordOptions.length > 0">
              <Tag
                v-for="item in keywordOptions"
                :key="item.value"
                :theme="getValidTheme(item.theme)"
                :variant="
                  selectedKeywords.includes(item.value) ? 'light' : 'outline'
                "
                @click="_handleKeywordClick(item.value)"
                :class="{
                  'tag-selected': selectedKeywords.includes(item.value),
                }"
                style="cursor: pointer; margin-right: 8px; margin-bottom: 4px"
              >
                {{ item.label }}（{{ item.count }}）
              </Tag>
            </div>
            <span v-else>-</span>
          </div>
        </div>

        <!-- 列表区 -->
        <div class="list-bar">
          <div class="list-header">
            <span class="result-count">
              共 <span class="highlight">{{ total }}</span> 条结果
            </span>
            <div class="list-actions">
              <Select
                v-model="sortType"
                :options="sortOptions"
                style="width: 120px; margin-right: 12px"
                placeholder="排序方式"
                @change="handleSortChange"
                size="small"
                :borderless="true"
              />
              <div class="view-switch">
                <div
                  class="switch-btn"
                  :class="{ active: viewType === 'card' }"
                  @click="viewType = 'card'"
                >
                  <ViewModuleIcon size="20" />
                </div>
                <div
                  class="switch-btn"
                  :class="{ active: viewType === 'list' }"
                  @click="viewType = 'list'"
                >
                  <ViewListIcon size="20" />
                </div>
              </div>
            </div>
          </div>

          <div class="loadingAndEmpty" v-if="listData.length === 0">
            <!-- 加载状态 -->
            <Loading v-if="loading" text="搜索中..." />

            <!-- 空结果状态 -->
            <Empty
              v-else-if="!loading && listData.length === 0"
              description="未找到相关结果，请尝试调整搜索条件"
            />
          </div>

          <!-- 搜索结果列表 -->
          <template v-if="!loading && listData.length > 0">
            <div class="table-container">
              <Table
                v-if="viewType === 'list'"
                :data="listData"
                :columns="columns"
                row-key="id"
                size="medium"
                bordered
                hover
                class="result-table"
                @row-click="handleItemClick"
              >
                <template #data_name="{ row }">
                  {{ row.infomation.data_name }}
                </template>
                <template #dataType="{ row }">
                  {{ row.dataType_text || '-' }}
                </template>
                <template #dataAuth="{ row }">
                  {{ row.dataAuth_text || '-' }}
                </template>
                <template #actions="{ row }">
                  <!-- <Button
                    shape="circle"
                    theme="default"
                    variant="text"
                    @click.stop="handleFavorite(row)"
                  >
                    <HeartIcon size="20" />
                  </Button> -->
                  <Collection
                    :is-favorite="row.isFavorite"
                    :row="row"
                    @collection-change="onCollectionChange(row, $event)"
                  />
                  <!-- <Button
                    shape="circle"
                    theme="default"
                    variant="text"
                    @click.stop="handleShare(row)"
                  >
                    <ShareIcon size="20" />
                  </Button> -->
                  <Share :row="row" />
                </template>
              </Table>
              <template v-if="viewType === 'card'">
                <div class="card-list">
                  <div
                    class="card-item"
                    v-for="item in listData"
                    :key="item.id"
                    @click="handleItemClick(item)"
                  >
                    <!-- 卡片右上角tag区域 -->
                    <div class="card-tags-overlay">
                      <!-- 共享状态tag -->
                      <Tag
                        v-if="item.dataAuth_text"
                        class="card-auth-tag"
                        size="small"
                        :style="{
                          backgroundColor: `${getDataAuthColor(item.dataAuth)}E6`,
                          color: 'white',
                          border: `1px solid ${getDataAuthColor(item.dataAuth)}`,
                        }"
                      >
                        {{ item.dataAuth_text }}
                      </Tag>
                      <!-- 资源类型tag -->
                      <Tag
                        v-if="item.dataType_text"
                        class="card-type-tag"
                        size="small"
                        :style="{
                          backgroundColor: `${getDataTypeColor(item.dataType)}E6`,
                          color: 'white',
                          border: `1px solid ${getDataTypeColor(item.dataType)}`,
                        }"
                      >
                        {{ item.dataType_text }}
                      </Tag>
                    </div>
                    <div class="card-img">
                      <img
                        :src="item.infomation.img"
                        :alt="item.infomation.data_name"
                      />
                    </div>
                    <div class="card-content">
                      <div class="card-title">
                        {{ item.infomation.data_name }}
                      </div>
                      <div class="card-tags">
                        <Tag v-for="tag in item.tags" :key="tag" size="small">
                          {{ tag }}
                        </Tag>
                      </div>
                      <div class="card-desc">
                        {{ item.infomation.describe || '-' }}
                      </div>

                      <!-- 关键词显示区域 -->
                      <div
                        class="card-keywords"
                        v-if="item.labelName && item.labelName.trim()"
                      >
                        <Tag
                          v-for="keyword in item.labelName
                            .split(',')
                            .map((k) => k.trim())
                            .filter((k) => k)"
                          :key="keyword"
                          size="small"
                          theme="primary"
                          variant="light"
                          style="margin-right: 4px; margin-bottom: 4px"
                        >
                          {{ keyword }}
                        </Tag>
                      </div>

                      <div class="card-meta">
                        <span>作者：{{ item.infomation.author || '-' }}</span>
                        <span>日期：{{ item.createTime || '-' }}</span>
                        <span>
                          浏览：{{ item.infomation.browsingCount || 0 }}
                        </span>
                      </div>
                      <div class="card-actions">
                        <Collection
                          :is-favorite="item.isFavorite"
                          :row="item"
                          @collection-change="onCollectionChange(item, $event)"
                        />
                        <Share :row="item" />
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>

            <!-- 分页 -->
            <div v-if="!loading && listData.length > 0" class="pagination-bar">
              <Pagination
                v-model="currentPage"
                :total="total"
                :total-content="false"
                :page-size="pageSize"
                :page-size-options="[10, 20, 50, 100]"
                show-jumper
                show-page-size
                @change="handlePageChange"
                @page-size-change="handlePageSizeChange"
              />
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 审批弹窗 -->
    <Dialog
      v-model:visible="showApprovalDialog"
      header="资源审批"
      :confirm-btn="{ content: '确认', theme: 'primary' }"
      :cancel-btn="{ content: '取消', theme: 'default' }"
      destroy-on-close
      @confirm="handleApproval"
      @cancel="closeApprovalModal"
    >
      <div class="approval-dialog-content">
        <p>您正在审批资源：{{ currentApprovalItem?.infomation.data_name }}</p>
        <p>共享方式：{{ currentApprovalItem?.dataAuth_text }}</p>
        <p>
          当前状态：{{
            currentApprovalItem?.isVisible === 1 ? '已通过' : '待审批'
          }}
        </p>
        <p>
          请选择审批结果：
          <RadioGroup
            v-model="approvalStatus"
            :options="[
              { label: '通过', value: 1 },
              { label: '不通过', value: 0 },
            ]"
          />
        </p>
      </div>
    </Dialog>
  </div>
</template>

<style scoped lang="less">
:deep(.t-loading) {
}

// Loading 蒙版样式
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(4px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: all; // 阻止点击穿透
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: loading-bounce 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

// Loading 过渡动画
.loading-fade-enter-active,
.loading-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.loading-fade-enter-from {
  opacity: 0;
  backdrop-filter: blur(0px);
}

.loading-fade-enter-to {
  opacity: 1;
  backdrop-filter: blur(4px);
}

.loading-fade-leave-from {
  opacity: 1;
  backdrop-filter: blur(4px);
}

.loading-fade-leave-to {
  opacity: 0;
  backdrop-filter: blur(0px);
}

// loading-content 弹入动画
@keyframes loading-bounce {
  0% {
    opacity: 0;
    transform: scale(0.7) translateY(-20px);
  }
  60% {
    opacity: 1;
    transform: scale(1.05) translateY(0);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.search-page-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  background: #f7f8fa;
  font-size: 14px;
}

.search-box-top {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  border-radius: 8px;
  padding: 24px 32px 16px 32px;
}

.main-content {
  display: flex;
  flex: 1;
  padding: 0 32px 32px 32px;
  gap: 24px;
}

.left-tree {
  width: 260px;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  min-height: 700px;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.tree-count {
  color: #999;
  margin-left: 4px;
}

.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.condition-bar {
  background: #fff;
  border-radius: 8px;
  padding: 16px 12px 8px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}
.condition-row {
  display: flex;
  align-items: flex-start;
  flex-wrap: nowrap;
  margin-bottom: 8px;
}
.condition-group,
.keyword-tags {
  display: flex;
  flex-wrap: nowrap;
  gap: 8px 16px;
  overflow-x: auto;
  white-space: nowrap;
}
.condition-label {
  flex-shrink: 0;
  font-weight: 600;
  margin-right: 12px;
  min-width: 64px;
  color: #222;
  font-size: 16px;
}
.result-count,
.result-count .highlight {
  font-size: 15px;
}
.result-table th,
.result-table td {
  font-size: 15px;
}
.card-title {
  font-size: 16px;
}
.card-desc,
.card-tags,
.card-meta {
  font-size: 14px;
}
.condition-group,
.keyword-tags,
.t-checkbox,
.t-tag {
  font-size: 14px;
}

.list-bar {
  background: #fff;
  border-radius: 8px;
  padding: 0 0 16px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  min-height: 600px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px 0 24px;
  flex-wrap: wrap;
}

.result-count {
  font-size: 16px;
  font-weight: bold;
}

.list-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  min-height: 120px;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f8f9fa;
  }
}

.item-img {
  position: relative;
  width: 120px;
  height: 90px;
  flex-shrink: 0;
  border-radius: 6px;
  overflow: hidden;
  background: #f5f5f5;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .item-type {
    position: absolute;
    left: 8px;
    top: 8px;
    z-index: 2;
  }
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.item-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 2px;
}

.item-tags {
  display: flex;
  gap: 4px;
  margin-bottom: 2px;
}

.item-desc {
  color: #666;
  font-size: 14px;
  margin-bottom: 2px;
}

.item-meta {
  color: #999;
  font-size: 12px;
  display: flex;
  gap: 16px;
}

.item-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 16px;
}

.pagination-bar {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px 0 24px;
}

.empty-state {
  padding: 40px;
  text-align: center;
  color: #666;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  p {
    margin-bottom: 16px;
  }
}

.empty-state-wrapper {
  width: 100%;
  height: 100%;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.loadingAndEmpty {
  width: 100%;
  height: 100%;
  padding-top: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.result-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(44, 90, 160, 0.04);
  overflow: hidden;
  margin-top: 16px;
  font-size: 15px;
}
.result-table th {
  background: #f7f8fa;
  font-weight: 600;
  color: #222;
  border-bottom: 1px solid #e5e6eb;
  padding: 14px 10px;
  text-align: center;
  font-size: 15px;
}
.result-table td {
  border-bottom: 1px solid #f0f0f0;
  padding: 14px 10px;
  text-align: center;
  vertical-align: middle;
  font-size: 15px;
}
.result-table tr:last-child td {
  border-bottom: none;
}
.result-table tr:hover td {
  background: #f5f7fa;
}
.result-table td:first-child,
.result-table th:first-child {
  text-align: left;
}
.result-table .t-button {
  margin: 0 4px;
  vertical-align: middle;
}
.result-table .t-button svg {
  font-size: 18px;
}

.list-actions .t-button.active {
  background: #e5e6eb;
  color: #2c5aa0;
}

.list-actions .t-button {
  transition: background 0.2s;
}
.list-actions .t-button:not(.active):hover {
  background: #f2f3f5;
}

.list-actions .t-select {
  min-width: 120px;
  border-radius: 6px;
  background: #fff;
}

.card-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: 16px;
}
.card-item {
  display: flex;
  align-items: center;
  border-radius: 14px;
  box-shadow: 0 2px 12px rgba(44, 90, 160, 0.06);
  border: 1px solid #f0f0f0;
  padding: 18px 20px;
  gap: 0;
  position: relative;
  transition:
    box-shadow 0.2s,
    border-color 0.2s;
  width: 100%;
  min-width: 0;
  margin-bottom: 0;
  &:hover {
    border-color: #2c5aa0;
    box-shadow: 0 4px 18px rgba(44, 90, 160, 0.13);
  }
}
.card-img {
  width: 120px;
  height: 90px;
  border-radius: 10px;
  overflow: hidden;
  flex-shrink: 0;
  margin-right: 24px;
  background: #f5f5f5;
  position: relative;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .card-type {
    position: absolute;
    left: 8px;
    top: 8px;
    z-index: 2;
  }
}
.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.card-title {
  font-size: 17px;
  font-weight: 600;
  margin-bottom: 2px;
}
.card-tags {
  display: flex;
  gap: 4px;
  margin-bottom: 0;
}
.card-desc {
  color: #666;
  font-size: 14px;
  margin-bottom: 2px;
  line-height: 1.5;
}
.card-meta {
  color: #999;
  font-size: 12px;
  display: flex;
  gap: 16px;
}
.card-actions {
  display: flex;
  gap: 8px;
  position: absolute;
  right: 24px;
  bottom: 18px;
  margin-top: 0;
  align-items: flex-end;
}

.view-switch {
  display: flex;
  align-items: center;
  background: #f5f6fa;
  border-radius: 24px;
  padding: 4px 6px;
  box-shadow: 0 1px 4px rgba(44, 90, 160, 0.04);
  .switch-btn {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    cursor: pointer;
    color: #666;
    transition:
      background 0.2s,
      color 0.2s;
    margin: 0 2px;
    &:hover {
      background: #e5e6eb;
    }
    &.active {
      background: #e5eefa;
      color: #2c5aa0;
    }
  }
}

.table-container {
  padding: 0 24px;
}

.tag-selected {
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(44, 90, 160, 0.1);
  border-color: #2c5aa0 !important;
  color: #fff !important;
  background: #2c5aa0 !important;
}

.resource-type-checkbox-group {
  display: flex;
  flex-wrap: wrap !important;
  gap: 12px 24px;
  align-items: flex-start;
  justify-content: flex-start;
  white-space: normal !important;
}

.tree-filter-input {
  width: 100%;
  margin-bottom: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-size: 14px;
}

.card-tags-overlay {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
  display: flex;
  flex-direction: row;
  gap: 6px;
  align-items: flex-start;
  justify-content: flex-end;
  pointer-events: none; /* 允许点击穿透到卡片 */
}

.card-auth-tag,
.card-type-tag {
  backdrop-filter: blur(6px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  font-weight: 500;
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 6px;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  pointer-events: auto;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 统一的自定义颜色tag样式 */
.card-auth-tag,
.card-type-tag {
  font-weight: 600 !important;
  border-radius: 6px !important;
  backdrop-filter: blur(6px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

// 响应式设计
@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
    gap: 16px;
  }

  .left-tree {
    width: 100%;
    min-height: auto;
  }
}

@media (max-width: 768px) {
  .search-page-container {
  }

  .main-content {
    padding: 0 16px 16px 16px;
  }

  .result-item {
    flex-direction: column;
    gap: 12px;
  }

  .item-img {
    width: 100%;
    height: 200px;
  }

  .item-actions {
    flex-direction: row;
    margin-left: 0;
  }
}

@media (max-width: 600px) {
  .list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  .list-actions {
    width: 100%;
    justify-content: flex-start;
  }
  .card-list {
    gap: 8px;
  }
  .card-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 8px 4px;
    min-width: 0;
    width: 100%;
  }
  .card-img {
    width: 100%;
    height: 120px;
    margin-right: 0;
    margin-bottom: 8px;
    border-radius: 8px;
  }
  .card-content {
    width: 100%;
    padding: 0 2px;
    gap: 4px;
  }
  .card-title {
    font-size: 15px;
  }
  .card-desc,
  .card-tags,
  .card-meta {
    font-size: 12px;
  }
  .card-actions {
    position: static;
    margin: 8px 0 0 0;
    justify-content: center;
    width: 100%;
  }
}

@media (max-width: 900px) {
  .table-container {
    padding: 0 8px;
  }
  .card-list {
    gap: 10px;
  }
  .card-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 10px 6px;
    min-width: 0;
    width: 100%;
  }
  .card-img {
    width: 100%;
    height: 100px;
    margin-right: 0;
    margin-bottom: 8px;
    border-radius: 8px;
  }
  .card-content {
    width: 100%;
    padding: 0 2px;
    gap: 4px;
  }
  .card-title {
    font-size: 16px;
  }
  .card-desc,
  .card-tags,
  .card-meta {
    font-size: 13px;
  }
  .card-actions {
    position: static;
    margin: 8px 0 0 0;
    justify-content: center;
    width: 100%;
  }
}
.highlight {
  color: #2c5aa0;
  font-weight: 600;
}

.approval-dialog-content {
  padding: 20px;
  text-align: left;
}

.approval-dialog-content p {
  margin-bottom: 10px;
  font-size: 15px;
}

.approval-dialog-content .t-radio-group {
  margin-top: 10px;
}
</style>
