<script setup lang="tsx">
import { ref, watchEffect } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

// 当前加载的组件实例，初始值为null
const currentComponent = ref(null);
// 组件加载状态，用于显示加载提示
const loading = ref(false);

// 定义可用的组件映射表
// 使用动态导入实现按需加载，提高性能
const components = {
  // 全文多模态解析重组工具组件
  'analytical-recombination': () =>
    import('./modules/analytical-recombination/analytical-recombination.vue'),
  // 关系挖掘组件
  relationship: () => import('./modules/relationship/relationship.vue'),
};

/**
 * 异步加载组件的函数
 * 根据路由参数activeIndex动态加载对应的组件
 */
const loadComponent = async () => {
  // 从路由查询参数中获取要加载的组件标识
  const activeIndex = route.query.activeIndex as string;

  // 检查参数是否有效，如果无效则显示默认占位
  if (!activeIndex || !components[activeIndex]) {
    currentComponent.value = null;
    return;
  }

  // 开始加载，显示加载状态
  loading.value = true;

  try {
    // 动态导入组件模块
    const module = await components[activeIndex]();
    // 将组件的默认导出设置为当前组件
    currentComponent.value = module.default;
  } catch (error) {
    // 加载失败时的错误处理
    console.error('组件加载失败:', error);
    currentComponent.value = null;
  } finally {
    // 无论成功还是失败，都要结束加载状态
    loading.value = false;
  }
};

// 使用watchEffect监听响应式依赖的变化
// 当route.query.activeIndex发生变化时，自动重新加载组件
watchEffect(() => {
  loadComponent();
});
</script>

<template>
  <div class="data-tools-detail">
    <!-- 加载状态显示 -->
    <div v-if="loading" class="loading">加载中...</div>
    <!-- 动态渲染当前组件 -->
    <component v-else-if="currentComponent" :is="currentComponent" />
    <!-- 默认占位组件 -->
    <div v-else class="placeholder">
      <div class="placeholder-content">
        <h3>请选择数据工具</h3>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
/* 加载状态的样式 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #666;
}

/* 默认占位样式 */
.placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;

  .placeholder-content {
    text-align: center;

    h3 {
      font-size: 18px;
      color: #333;
      margin-bottom: 12px;
    }

    p {
      font-size: 14px;
      color: #666;
    }
  }
}
</style>
