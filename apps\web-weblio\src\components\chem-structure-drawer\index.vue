<script setup lang="ts">
import type { CSSProperties } from 'vue';

import { computed, ref, watch } from 'vue';

import Ketcher from './ketcher/index.vue';

const props = defineProps<{
  modelValue: any;
  visible: boolean;
}>();
const emits = defineEmits([
  'update:visible',
  'update:modelValue',
  'ok',
  'cancel',
]);

const localValue = ref(props.modelValue);
const visible = computed(() => props.visible);

watch(
  () => props.modelValue,
  (val) => {
    localValue.value = val;
  },
);

const modalRef = ref<HTMLElement | null>(null);
const headerRef = ref<HTMLElement | null>(null);

const modalStyle: CSSProperties = {
  minWidth: '640px',
  minHeight: '480px',
  background: '#fff',
  borderRadius: '8px',
  boxShadow: '0 2px 12px rgba(0,0,0,0.2)',
  position: 'fixed',
  left: '50%',
  top: '20%',
  transform: 'translate(-50%, 0)',
  zIndex: 1001,
  display: 'flex',
  flexDirection: 'column',
};

const handleOk = () => {
  console.log(localValue.value);
  emits('update:modelValue', localValue.value);
  emits('ok', localValue.value);
  emits('update:visible', false);
};
const handleCancel = () => {
  emits('cancel');
  emits('update:visible', false);
};
</script>

<template>
  <div v-if="visible" class="chem-drawer-mask">
    <div class="chem-drawer-modal" ref="modalRef" :style="modalStyle">
      <div class="chem-drawer-header" ref="headerRef">
        <span>化学式查询</span>
        <button class="chem-drawer-close" @click="handleCancel">×</button>
      </div>
      <div class="chem-drawer-body">
        <Ketcher v-model="localValue" style="height: 400px; width: 600px" />
      </div>
      <div class="chem-drawer-footer">
        <button class="chem-btn" @click="handleCancel">取消</button>
        <button class="chem-btn chem-btn-primary" @click="handleOk">
          确认
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chem-drawer-mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.2);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.chem-drawer-modal {
  min-width: 640px;
  min-height: 480px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  position: relative;
}
.chem-drawer-header {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  font-size: 18px;
  font-weight: bold;
  border-bottom: 1px solid #f0f0f0;
  cursor: move;
  user-select: none;
}
.chem-drawer-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
}
.chem-drawer-body {
  flex: 1;
  padding: 16px 24px;
  overflow: auto;
}
.chem-drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 12px 24px 16px 24px;
  border-top: 1px solid #f0f0f0;
}
.chem-btn {
  min-width: 80px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  background: #fff;
  cursor: pointer;
  font-size: 14px;
}
.chem-btn-primary {
  background: #1677ff;
  color: #fff;
  border: none;
}
</style>
