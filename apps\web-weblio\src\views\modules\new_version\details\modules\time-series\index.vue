<script setup lang="tsx">
import { computed, h, onMounted, ref, toRefs, watch } from 'vue';

import DetailHeader from '../common/detail-header';
import DetailLayout from '../common/detail-layout';
import TopCard from '../common/top-card';
import { getExcelExport, getLiteratureDetail } from './api';
import LineECharts from './lineECharts.vue';
import { Button, Option, Select } from 'tdesign-vue-next';
import { DownloadIcon } from 'tdesign-icons-vue-next';

const props = defineProps<{
  item: any;
}>();

const { item } = toRefs(props);

const downloadIcon = () => h(DownloadIcon, { size: '24px' });

interface DetailBodyData {
  subItem: string;
  dataDescribe: string;
  dataTableName: string;
  classification: string;
}

const detailBodyData = ref([]);

const detailHeaderData = ref<{ version?: string; dataLevelName?: string; source?: string; categoryName?: string; }>({});

const selectedOption = ref(''); // 默认选择空值

// 将 selectedOption 改为一个对象，每个下拉框对应一个独立的选中值
const selectedOptions = ref<{ [key: string]: string }>({});


// 在组件挂载时初始化每个下拉框的默认值
onMounted(() => {
  detailBodyData.value.forEach((item, index) => {
    if (item.classification && item.classification.length > 0) {
      selectedOptions.value[index] = item.classification[0];
    }
  });
});

// 监听 item.classification 的变化并更新默认值
watch(
  () => item.value?.classification,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      // 更新所有下拉框的默认值
      detailBodyData.value.forEach((item, index) => {
        selectedOptions.value[index] = newVal[0];
      });
    }
  },
  { immediate: true }
);

const chartOptions = ref({}); // ECharts 配置项
const searchTypeOptions = [
  {
    label: '去年产量',
    value: '去年产量'
  },
  {
    label: '今年产量',
    value: '今年产量'
  }
];

// 获取详情数据（示例：实际应替换为API调用）
const fetchDetailData = async () => {
  // 示例数据，实际应用中替换为API请求
  const params = {
    baseCode: '62ce57d846e74c1998193a4a796449c6-1',
    isFile: '1',
    dataType: '8'
  }
  const res = await getLiteratureDetail(params);
  detailHeaderData.value = res.headerData;
  // detailBodyData.value = res.bodyData;
  detailBodyData.value = res.bodyData.map((item, index) => {
    const classificationArray = getClassificationArray(item.classification);

    // 初始化每个下拉框的默认值
    if (classificationArray.length > 0) {
      selectedOptions.value[index] = classificationArray[0];
    }

    const exportParams = {
      tableName: item.dataTableName,
      type: ''
    }

    return {
      title: item.subItem,
      content: () => (
        <div>
          <div style={{ fontWeight: 'bold', fontSize: '18px', marginBottom: '12px' }}>数据描述</div>
          <div style={{ marginBottom: '24px' }}>{item.dataDescribe}</div>
          <div style={{ fontWeight: 'bold', fontSize: '18px', marginBottom: '12px' }}>数据详情</div>
          <div style="display: flex; justify-content: flex-end; align-items: center; gap: 12px;">
            <Select v-model={selectedOptions.value[index]} class="custom-select">
              {classificationArray.map((label) => (
                <Option label={label} value={label} />
              ))}
            </Select>
            <Button onClick={() => exportData(exportParams)} icon={downloadIcon} class="custom-button">源数据下载</Button>
          </div>
          <LineECharts seriesData={[
            {
              name: '去年产量',
              type: 'line',
              data: [100, 140, 230, 100, 130]
            },
            {
              name: '今年产量',
              type: 'line',
              data: [150, 100, 200, 140, 100]
            }
          ]}
            xAxisData={['1月', '2月', '3月', '4月', '5月']}
            title=""
          />
        </div>
      )
    }
  })


  // 根据 selectedOption 更新 chartOptions
  watch(selectedOption, (newValue) => {
    updateChartOptions(newValue);
  });

  updateChartOptions(selectedOption.value); // 初始化图表选项

  console.log(detailBodyData.value, '文献详情');
  console.log(item.value, 'itemitemitemitemitemitemitemitemitemitemitemitemitem');
};


// 工具函数：统一处理 classification 数据
const getClassificationArray = (classification: any): string[] => {
  if (typeof classification === 'string') {
    try {
      return JSON.parse(classification.replace(/'/g, '"'));
    } catch (e) {
      return classification.split(',').map(s => s.trim());
    }
  } else if (Array.isArray(classification)) {
    return classification;
  }
  return [];
};

const updateChartOptions = (option: string) => {
  // 示例数据，实际应用中根据 option 动态生成
  const data = [
    { month: '1月', lastYear: 190, thisYear: 150 },
    { month: '2月', lastYear: 140, thisYear: 110 },
    { month: '3月', lastYear: 230, thisYear: 290 },
    { month: '4月', lastYear: 140, thisYear: 140 },
    { month: '5月', lastYear: 130, thisYear: 180 },
  ];

  const seriesData = data.map((item) => ({
    name: option === '去年产量' ? '去年产量' : '今年产量',
    type: 'line',
    data: option === '去年产量' ? data.map((d) => d.lastYear) : data.map((d) => d.thisYear),
  }));

  chartOptions.value = {
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: data.map((item) => item.month),
    },
    yAxis: {
      type: 'value',
    },
    series: seriesData,
  };
};

// 新增导出数据函数
const exportData = async (params: any) => {
  const response = await getExcelExport(params)
  const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = 'export.xlsx'; // 文件名
  link.click();
  URL.revokeObjectURL(link.href);
};

onMounted(async () => {
  fetchDetailData();
});

const data = computed(() => ({
  topCard: () => <TopCard data={item} />,
  menu: detailBodyData.value,
  // menu: [
  //   {
  //     title: '概览',
  //     content: () => <div>Document</div>,
  //   },
  //   {
  //     title: '文字内容',
  //     content: () => <div>Document</div>,
  //   },
  //   {
  //     title: '引用方式',
  //     content: () => <div>Document</div>,
  //   },
  // ],
  right: [
    {
      title: '分类',
      content: () => <div>{item.value?.categoryName}</div>,
    },
    {
      title: '当前版本',
      content: () => <div>{detailHeaderData.value?.version}</div>,
    },
    {
      title: '数据分级',
      content: () => <div>{detailHeaderData.value?.dataLevelName}</div>,
    },
    {
      title: '数据来源',
      content: () => <div>{detailHeaderData.value?.source}</div>,
    },
  ],
}));
</script>

<template>
  <DetailHeader />
  <DetailLayout :data="data" />
</template>

<style scoped lang="scss">
:deep(.custom-select) {
  width: 10%;
}

.custom-button {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-left: 10px;
}
</style>
