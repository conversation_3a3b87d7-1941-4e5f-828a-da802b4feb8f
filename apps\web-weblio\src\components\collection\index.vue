<script setup lang="tsx">
import { saveFavorites } from '#/views/modules/tPortalFavorites/api';
import { HeartFilledIcon, HeartIcon } from 'tdesign-icons-vue-next';
import { Button, MessagePlugin } from 'tdesign-vue-next';
import { defineEmits, ref } from 'vue';

const props = defineProps<{
  isFavorite: boolean;
  row: any;
}>();

const emits = defineEmits(['update:isFavorite', 'collectionChange']);

const localFavorite = ref(props.isFavorite);

const handleCollection = async () => {
  const prev = localFavorite.value;
  localFavorite.value = !prev;
  try {
    const { dataType, baseCode, infomation } = props.row;
    await saveFavorites({
      dataType,
      operationCode: baseCode,
      dataInformation: JSON.stringify(infomation) || '',
    });
    emits('update:isFavorite', localFavorite.value);
    emits('collectionChange', {
      isFavorite: localFavorite.value,
      row: props.row,
    });
    MessagePlugin.success(localFavorite.value ? '收藏成功' : '取消收藏成功');
  } catch {
    localFavorite.value = prev;
    MessagePlugin.error('操作失败');
  }
};
</script>

<template>
  <Button
    shape="circle"
    theme="default"
    variant="text"
    @click.stop="handleCollection()"
  >
    <component
      :is="localFavorite ? HeartFilledIcon : HeartIcon"
      size="20"
      :style="{ color: localFavorite ? '#FF4D4F' : '#C0C4CC' }"
    />
  </Button>
</template>

<style scoped lang="less"></style>
