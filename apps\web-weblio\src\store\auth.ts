import type { Recordable, UserInfo } from '@vben/types';

import {
  dingtalk<PERSON>oginApi,
  getAccessCodes<PERSON>pi,
  getAuthCaptchaImageApi,
  getUserInfoApi,
  loginApi,
  logoutApi,
  sendSmsCodeApi,
  updateMyPassword,
} from '#/api';
import { DEFAULT_HOME_PATH_CLIENT, LOGIN_PATH_CLIENT } from '@vben/constants';
import { $t } from '@vben/locales';
import { resetAllStores, useAccessStore, useUserStore } from '@vben/stores';
import { defineStore } from 'pinia';
import { NotifyPlugin } from 'tdesign-vue-next';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

export const useAuthStore = defineStore('auth', () => {
  const accessStore = useAccessStore();
  const userStore = useUserStore();
  const router = useRouter();

  const loginLoading = ref(false);

  const crossOriginStore = ref(null);

  const setCrossOriginStore = (store: any) => {
    crossOriginStore.value = store;
  };

  const getCrossOriginStore = () => {
    return crossOriginStore.value;
  };

  const getAuthCaptchaImage = (captchaKey: any) => {
    return getAuthCaptchaImageApi(captchaKey);
  };

  const sendSmsCode = (phoneNumber: any, captcha?: string, checkKey?: string, type?: string) => {
    return sendSmsCodeApi(phoneNumber, captcha, checkKey, type);
  };

  async function resetPassword(
    params: Recordable<any>,
    redirect: boolean = true,
  ) {
    try {
      await updateMyPassword(params);
    } catch {
      // 不做任何处理
    }
    resetAllStores();
    accessStore.setLoginExpired(false);

    // 回登录页带上当前路由地址
    await router.replace({
      path: LOGIN_PATH_CLIENT,
      query: redirect
        ? {
            redirect: encodeURIComponent(router.currentRoute.value.fullPath),
          }
        : {},
    });
  }

  /**
   * 异步处理登录操作
   * Asynchronously handle the login process
   * @param params 登录表单数据
   */
  async function authLoginDing(
    params: Recordable<any>,
    onSuccess?: () => Promise<void> | void,
  ) {
    // 异步处理用户登录操作并获取 accessToken
    let userInfo: null | UserInfo = null;
    try {
      loginLoading.value = true;
      const res: any = await dingtalkLoginApi(params);
      const { token } = res;
      // 如果成功获取到 accessToken
      if (token) {
        // 将 accessToken 存储到 accessStore 中
        accessStore.setAccessToken(token);

        // 获取用户信息并存储到 accessStore 中
        const [fetchUserInfoResult, accessCodes] = await Promise.all([
          fetchUserInfo(),
          getAccessCodesApi(),
        ]);

        userInfo = fetchUserInfoResult;
        console.log('userInfo', userInfo);

        userStore.setUserInfo(userInfo);
        accessStore.setAccessCodes(accessCodes);

        if (accessStore.loginExpired) {
          accessStore.setLoginExpired(false);
        } else {
          onSuccess
            ? await onSuccess?.()
            : await router.push(userInfo?.homePath || DEFAULT_HOME_PATH_CLIENT);
        }

        if (userInfo?.realName) {
          NotifyPlugin.success({
            title: $t('authentication.loginSuccess'),
            content: `${$t('authentication.loginSuccessDesc')}:${userInfo?.realName}`,
            duration: 3000,
            closeBtn: true,
          });
          router.push('/home');
        }
      }
    } finally {
      loginLoading.value = false;
    }

    return {
      userInfo,
    };
  }

  /**
   * 异步处理登录操作
   * Asynchronously handle the login process
   * @param params 登录表单数据
   */
  async function authLogin(
    params: Recordable<any>,
    onSuccess?: () => Promise<void> | void,
  ) {
    // 异步处理用户登录操作并获取 accessToken
    let userInfo: null | UserInfo = null;
    try {
      loginLoading.value = true;
      const res: any = await loginApi(params);
      const { token } = res;
      // 如果成功获取到 accessToken
      if (token) {
        // 将 accessToken 存储到 accessStore 中
        accessStore.setAccessToken(token);

        // 获取用户信息并存储到 accessStore 中
        const [fetchUserInfoResult, accessCodes] = await Promise.all([
          fetchUserInfo(),
          getAccessCodesApi(),
        ]);

        userInfo = fetchUserInfoResult;

        userStore.setUserInfo(userInfo);
        accessStore.setAccessCodes(accessCodes);

        if (accessStore.loginExpired) {
          accessStore.setLoginExpired(false);
        } else {
          onSuccess ? await onSuccess?.() : null;
        }

        if (userInfo?.realName) {
          NotifyPlugin.success({
            title: $t('authentication.loginSuccess'),
            content: `${$t('authentication.loginSuccessDesc')}:${userInfo?.realName}`,
            duration: 3000,
            closeBtn: true,
          });
          router.push('/home');
        }
      }
    } finally {
      loginLoading.value = false;
    }

    return {
      userInfo,
    };
  }

  /**
   * 异步处理手机号登录操作
   * Asynchronously handle the phone number login process
   * @param params 手机号登录表单数据
   */
  async function authPhoneNumberLogin(
    params: Recordable<any>,
    onSuccess?: () => Promise<void> | void,
  ) {
    // 异步处理用户手机号登录操作并获取 accessToken
    let userInfo: null | UserInfo = null;
    try {
      loginLoading.value = true;
      const res: any = await phoneNumberLoginApi(params);
      const { token } = res;
      // 如果成功获取到 accessToken
      if (token) {
        // 将 accessToken 存储到 accessStore 中
        accessStore.setAccessToken(token);

        // 获取用户信息并存储到 accessStore 中
        const [fetchUserInfoResult, accessCodes] = await Promise.all([
          fetchUserInfo(),
          getAccessCodesApi(),
        ]);

        userInfo = fetchUserInfoResult;

        userStore.setUserInfo(userInfo);
        accessStore.setAccessCodes(accessCodes);

        if (accessStore.loginExpired) {
          accessStore.setLoginExpired(false);
        } else {
          onSuccess
            ? await onSuccess?.()
            : null
        }

        if (userInfo?.realName) {
          NotifyPlugin.success({
            title: $t('authentication.loginSuccess'),
            content: `${$t('authentication.loginSuccessDesc')}:${userInfo?.realName}`,
            duration: 3000,
            closeBtn: true,
          });
          router.push('/home')
        }
      }
    } finally {
      loginLoading.value = false;
    }

    return {
      userInfo,
    };
  }

  async function logout(redirect: boolean = true) {
    try {
      await logoutApi();
    } catch {
      // 不做任何处理
    }
    resetAllStores();
    accessStore.setLoginExpired(false);

    // 回登录页带上当前路由地址
    await router.replace({
      path: LOGIN_PATH_CLIENT,
      query: redirect
        ? {
            redirect: encodeURIComponent(router.currentRoute.value.fullPath),
          }
        : {},
    });
  }

  async function logoutWebLio() {
    try {
      await logoutApi();
    } catch {
      // 不做任何处理
    }
    // 刷新页面
    window.location.reload();
    resetAllStores();
    accessStore.setLoginExpired(false);

    // router.push({ name: '/home' });
  }

  // 跳转到登录页
  function goLoginPage() {
    const params = router.currentRoute.value.query;
    router.replace({
      path: LOGIN_PATH_CLIENT,
      query: params,
    });
  }

  async function fetchUserInfo() {
    let userInfo: null | UserInfo = null;
    userInfo = await getUserInfoApi();
    userStore.setUserInfo(userInfo);
    return userInfo;
  }

  function $reset() {
    loginLoading.value = false;
  }

  return {
    $reset,
    authLogin,
    authLoginDing,
    fetchUserInfo,
    loginLoading,
    logout,
    logoutWebLio,
    goLoginPage,
    setCrossOriginStore,
    getCrossOriginStore,
    resetPassword,
    getAuthCaptchaImage,
    sendSmsCode,
    authPhoneNumberLogin,
  };
});
