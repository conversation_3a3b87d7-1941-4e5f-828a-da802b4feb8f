<script setup lang="tsx">
import { defineProps, reactive, ref, withDefaults } from "vue";
import { useRequest } from "vue-hooks-plus";

import { useVbenModal } from "@vben/common-ui";

import { Input, Select, Textarea, TreeSelect } from "tdesign-vue-next";
import { Form, FormItem, type FormProps } from "tdesign-vue-next";

import { getDictItems } from "#/api";

import { saveAccountExtension, getDeptList } from "../api";

/**
 * 属性定义
 */
interface Props {
  outRef?: {
    reload?: () => void;
  } | null;
}

const props = withDefaults(defineProps<Props>(), {
  outRef: null
});
/**
 * 表单数据类型定义
 */
interface FormData {
  id?: string;
  accountNumber?: string;
  password?: string;
  realName?: string;
  deptId?: string;
  phoneNumber?: string;
  email?: string;
  status?: string;
  delFlag?: string;
  remark?: string;
  sourceSystem?: string;
}

/**
 * 表单组件操作句柄
 */
const form = ref();
/**
 * 表单数据
 */
const formData = ref<FormData>({});
/**
 * form 字段校验规则
 */
const FORM_RULES: FormProps["rules"] = {
  accountNumber: [
    {
      required: true,
      message: "账号必填"
    }
  ],
  password: [
    {
      required: true,
      message: "密码必填"
    }
  ],
  realName: [
    {
      required: true,
      message: "姓名必填"
    }
  ],
  // deptId: [
  //   {
  //     validator: (val: any) => {
  //       // 只有sourceSystem为"02"(所内用户)或"04"(自建用户)时部门才必填
  //       const sourceSystem = formData.value.sourceSystem || state.tagObj.sourceSystem;
  //       console.log(sourceSystem);
  //       if ((sourceSystem === '02' || sourceSystem === '04') && (!val || val === '')) {
  //         return { result: false, message: '部门必填' };
  //       }
  //       return { result: true, message: '' };
  //     }
  //   }
  // ],
  phoneNumber: [
    {
      required: true,
      message: "手机号必填"
    },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号格式"
    }
  ],
  email: [
    {
      pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: "请输入正确的邮箱格式"
    }
  ],
  // status: [
  //   {
  //     required: true,
  //     message: "状态必填"
  //   }
  // ],
  // delFlag: [
  //   {
  //     required: true,
  //     message: "注销状态必填"
  //   }
  // ]
};

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {} as FormData
});
const reqRunner = {
  saveAccountExtension: useRequest(saveAccountExtension, {
    /**
     * 手动出发请求
     */
    manual: true,
    /**
     * 300毫秒防抖
     */
    debounceWait: 300,
    /**
     * 错误处理防范
     */
    onError: () => {
    },
    /**
     * 成功处理方法发
     * @param res
     */
    onSuccess: (res: any) => {
      /**
       * 执行外部刷新方法
       */
      props.outRef?.reload?.();
      /**
       * 初始化静态数据
       */
      initStatus();
      /**
       * 关闭弹出框
       */
      modalApi.close();
    }
  })
};
const status = ref([]);
const delFlag = ref([]);
const deptTree = ref([]);

function listToTree(list, parentId = '0') {
  return list
    .filter(item => item.parentId === parentId)
    .map(item => ({
      value: item.id,
      label: item.deptName,
      children: listToTree(list, item.id)
    }));
}

/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};
/**
 * 弹出框定义
 */
const [Modal, modalApi] = useVbenModal({

  closeOnClickModal: false,
  /**
   * 打开状态变化时执行
   * @param isOpen
   */
  onOpenChange: async (isOpen: boolean) => {
    /**
     * 若关闭则初始化内部静态数据
     */
    if (isOpen) {
      status.value = await getDictItems("ACCOUNT_STATUS");
      delFlag.value = await getDictItems("ACCOUNT_DEL_FLAG");
      // 获取部门树
      const deptList = await getDeptList({});
      deptTree.value = listToTree(deptList);
    } else {
      initStatus();
    }
  },
  /**
   * 弹出框确认按钮相应
   */
  onConfirm: async () => {
    /**
     * 执行form表单校验
     */
    const vali = await form.value.validate();
    /**
     * 校验通过
     */
    if (vali === true) {
      /**
       * 保存数据
       */
      if (state.tagObj?.id) {
        // 编辑
        reqRunner.saveAccountExtension.run({ ...state.tagObj, ...formData.value});
      } else {
        // 新建
        reqRunner.saveAccountExtension.run({ ...state.tagObj, ...formData.value, sourceSystem: '04' });
      }
    }
  },
  /**
   * 弹出框取消按钮相应
   */
  onCancel: () => {
    /**
     * 初始化静态数据
     */
    initStatus();
    /**
     * 关闭弹出框
     */
    modalApi.close();
  }
});
/**
 * 供外部调用的方法：打开窗体
 * @param data 外部传入数据
 */
const open = async (data?: any) => {
  /**
   * 若数据存在则设置内部操作对象实体
   */
  if (data) {
    state.tagObj = data;
    formData.value = data;
  }
  /**
   * 打开弹出窗
   */
  modalApi.open();
};
/**
 * 导出资源
 */
defineExpose({
  open
});
</script>

<template>
  <Modal :title="state.tagObj?.id ? `编辑` : `新建`" class="w-[40%]">
    <Form
      ref="form"
      :data="formData"
      :rules="FORM_RULES"
      class="w-full"
      label-align="top"
    >
      <div class="grid w-full grid-cols-1 gap-1">
        <FormItem label="账号" name="accountNumber">
          <Input
            v-model="formData.accountNumber"
            :disabled="state.tagObj?.id ? true : false"
            clearable
            placeholder="请输入内容"
          />
        </FormItem>
        <FormItem v-if="!state.tagObj?.id" label="密码" name="password">
          <Input
            v-model="formData.password"
            clearable
            placeholder="请输入内容"
            type="password"
          />
        </FormItem>
        <FormItem label="姓名" name="realName">
          <Input
            v-model="formData.realName"
            clearable
            placeholder="请输入姓名"
          />
        </FormItem>
        <FormItem label="部门" name="deptId">
          <TreeSelect
            v-model="formData.deptId"
            :data="deptTree"
            clearable
            placeholder="请选择部门"
            tree-default-expand-all
            style="width: 100%"
          />
        </FormItem>
        <FormItem label="手机号" name="phoneNumber">
          <Input
            v-model="formData.phoneNumber"
            clearable
            placeholder="请输入手机号"
          />
        </FormItem>
        <FormItem label="邮箱" name="email">
          <Input
            v-model="formData.email"
            clearable
            placeholder="请输入邮箱"
          />
        </FormItem>
        <!-- <FormItem label="状态" name="status">
          <Select
            v-model="formData.status"
            :options="status"
            clearable
            placeholder="选择状态"
            style="width: 100%"
          />
        </FormItem>
        <FormItem label="注销状态" name="delFlag">
          <Select
            v-model="formData.delFlag"
            :options="delFlag"
            clearable
            placeholder="选择注销状态"
            style="width: 100%"
          />
        </FormItem> -->
        <FormItem label="备注" name="remark">
          <Textarea
            v-model="formData.remark"
            clearable
            placeholder="请输入内容"
          />
        </FormItem>
      </div>
    </Form>
  </Modal>
</template>

<style scoped></style>
