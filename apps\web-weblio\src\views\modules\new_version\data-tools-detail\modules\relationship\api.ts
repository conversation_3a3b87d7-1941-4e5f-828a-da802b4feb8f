import { requestClient } from '#/api/request';

// 知识对象抽取API
export async function extractKnowledge(data: FormData) {
  return requestClient.post('/api/extract', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 60_000,
  });
}

// 跨模态对齐API
export async function alignKnowledge(data: FormData) {
  return requestClient.post('/api/align', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 60_000,
  });
}
