import { requestClient } from '#/api/request';

// 上传PDF文件API
export async function uploadPdfs(data: FormData, taskName: string) {
  return requestClient.post(
    `/api/upload_pdfs?task_name=${encodeURIComponent(taskName)}`,
    data,
    {
      headers: { 'Content-Type': 'multipart/form-data' },
      timeout: 300_000, // 5分钟超时
    },
  );
}

// 获取上传记录API
export async function getUploadList() {
  return requestClient.get('/api/upload_list');
}

// 下载文件API
export async function downloadFile(sessionId: string) {
  return requestClient.get('/api/get_zip_by_session_id', {
    params: { session_id: sessionId },
    responseType: 'blob',
  });
}
