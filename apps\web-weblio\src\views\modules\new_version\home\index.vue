<script setup lang="ts">
import type { StatisticItem } from '#/components/statistics-overview/index.vue';

import { getDictItems } from '#/api';
import SearchBox from '#/components/search-box/index.vue';
import StatisticsOverview from '#/components/statistics-overview/index.vue';
import { useSearchStore } from '#/store/search';
import { onMounted, onUnmounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { getResourcesTypeWithCount } from '../search-page/api';
import { getServiceHome, getStatistics } from './api';
import DataCategory from './modules/DataCategory.vue';
import DataTools from './modules/DataTools.vue';

const searchStore = useSearchStore();
const router = useRouter();
// 响应式数据
const searchMode = ref<'ai' | 'search'>('search');
const selectedSearchType = ref('all');
const searchQuery = ref('');
const aiQuery = ref('');
const deepSearchEnabled = ref(false);

// AI问答示例
const aiExamples = ref([]);

// 统计数据 - 初始化默认数据
const statisticsData = ref<StatisticItem[]>([]);

// mock数据
const categoryList = ref([]);
const toolsList = ref<any[]>([]);
const initHomeDisplayList = () => {
  // getHomeDisplayList().then((res) => {
  //   categoryList.value = res;
  // });
  getResourcesTypeWithCount({}).then((res) => {
    categoryList.value = res;
  });
};
const handleCategoryMore = () => {
  // TODO: 实现数据分类更多功能
};
const handleCategoryClick = (item: any) => {
  // 设置搜索类型到store
  searchStore.setSelectedSearchType(item.value);
  // 跳转到搜索页面
  router.push('/search-page');
};
const handleToolsMore = () => {
  // TODO: 实现数据工具更多功能
};
const handleToolClick = (_item: any) => {
  // TODO: 实现工具点击功能
  // router.push('/tSysDataTools');
  router.push({
    path: '/data-tools-detail',
    query: {
      // activeIndex: 'analytical-recombination',
      activeIndex: 'relationship',
    },
  });
};

// 处理统计卡片点击
const handleStatisticClick = (_item: StatisticItem) => {
  // 这里可以添加跳转到具体统计页面的逻辑
};
// 处理高级检索
const handleAdvancedSearch = () => {};

// 处理结构式检索
const handleFormulaSearch = () => {
  // 这里添加结构式检索逻辑
};
// 处理AI问答
const handleAiChat = (_data: { deepSearch: boolean; query: string }) => {
  // 这里添加AI问答逻辑
  // 根据data.deepSearch决定是否使用深度搜索
};
// 处理搜索
const handleSearch = (_data: { query: string; type: string }) => {
  // 跳转到搜索页面并传递搜索参数
};
function initStatisticsData() {
  Promise.all([
    getStatistics({
      esIndex: import.meta.env.VITE_ES_INDEX,
    }),
    getDictItems('HOME_STATISTICS'),
  ]).then((res) => {
    const [statisticsDataRes, dictItems] = res;

    const newStatisticsData = dictItems.map((item, index) => {
      const statistic = statisticsDataRes.find(
        (statistic) => statistic.value === item.value,
      );
      const colors = [
        '#7c3aed',
        '#ec4899',
        '#06b6d4',
        '#10b981',
        '#f59e0b',
        '#84cc16',
      ];
      // const icons = [
      //   'FolderIcon',
      //   'DatabaseIcon',
      //   'HardDriveIcon',
      //   'FileTextIcon',
      //   'ToolIcon',
      //   'UsersIcon',
      // ];
      const iconMap: any = {
        dataLiterature: 'FileTextIcon',
        dataset: 'FolderIcon',
        storageCapacity: 'HardDriveIcon',
        dataTool: 'ToolIcon',
      };
      return {
        ...statistic,
        key: statistic.value,
        title: statistic.label,
        value: statistic.count || 0,
        unit: statistic.unit,
        icon: iconMap[statistic.value] || 'FolderIcon',
        color: colors[index % colors.length] || '#7c3aed',
      };
    });
    statisticsData.value = newStatisticsData;
  });
}
const initServiceHome = () => {
  getServiceHome().then((res) => {
    toolsList.value = res;
  });
};
onMounted(() => {
  searchStore.setInitCategory(0);
  initHomeDisplayList();
  initStatisticsData();
  initServiceHome();
});
// 组件卸载时清理资源 - 防止内存泄漏
onUnmounted(() => {});
</script>

<template>
  <div class="home-container">
    <!-- 使用搜索组件 -->
    <SearchBox
      v-model:search-mode="searchMode"
      v-model:selected-search-type="selectedSearchType"
      v-model:search-query="searchQuery"
      v-model:ai-query="aiQuery"
      v-model:deep-search-enabled="deepSearchEnabled"
      :ai-examples="aiExamples"
      @search="handleSearch"
      @ai-chat="handleAiChat"
      @advanced-search="handleAdvancedSearch"
      @formula-search="handleFormulaSearch"
    />
    <!-- 统计数据概览 -->
    <StatisticsOverview
      :data="statisticsData"
      @card-click="handleStatisticClick"
    />
    <!-- 横向排列的数据分类和数据工具 -->
    <div class="category-tools-row">
      <div class="category-col">
        <DataCategory
          title="数据分类"
          :list="categoryList"
          :on-more="handleCategoryMore"
          :on-card-click="handleCategoryClick"
        />
      </div>
      <div class="tools-col">
        <DataTools
          title="数据工具"
          :list="toolsList"
          :on-more="handleToolsMore"
          :on-tool-click="handleToolClick"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-container {
  width: 100%;

  box-sizing: border-box;
}

.image-layout-section {
  margin-top: 40px;
}

/* 为统计概览组件调整边距 */
.home-container > :deep(.statistics-overview) {
  margin: 20px 0;
}

.category-tools-row {
  display: flex;
  gap: 40px;
  margin-top: 16px;
  margin-bottom: 32px;
  align-items: stretch;
  padding: 0 48px;
}
.category-col {
  flex: 2;
  min-width: 0;
}
.tools-col {
  flex: 1;
  min-width: 340px;
}

:deep(.data-category),
:deep(.data-tools) {
  height: 100%;
  min-height: 420px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
:deep(.data-category .card-list) {
  min-height: 320px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .home-container {
  }
  .category-tools-row {
    gap: 20px;
  }
  .tools-col {
    min-width: 0;
  }
}
@media (max-width: 900px) {
  .category-tools-row {
    flex-direction: column;
    gap: 24px;
    margin-top: 24px;
  }
  .tools-col {
    min-width: 0;
  }
  :deep(.data-category),
  :deep(.data-tools) {
    min-height: 320px;
  }
}
@media (max-width: 480px) {
  .home-container {
    padding: 0 4px;
  }
  .category-tools-row {
    flex-direction: column;
    margin-top: 4px;
    gap: 6px;
    padding: 0 1px;
    margin-bottom: 8px;
  }
  .category-col,
  .tools-col {
    min-width: 0;
    padding: 0;
    width: 100%;
  }
  :deep(.data-category),
  :deep(.data-tools) {
    min-height: 120px;
    padding: 4px 0;
  }
  :deep(.data-category .card-list),
  :deep(.data-tools .card-list) {
    display: flex;
    flex-direction: column;
    gap: 6px;
    min-height: 60px;
  }
  :deep(.data-category .card-list .t-card),
  :deep(.data-tools .card-list .t-card) {
    width: 100% !important;
    margin: 0 auto;
    font-size: 12px;
    padding: 8px 4px;
  }
  :deep(.t-card__title) {
    font-size: 14px;
  }
  :deep(.statistics-overview) {
    margin: 8px 0;
  }
  :deep(.search-box) {
    margin-bottom: 6px;
  }
  :deep(.t-card img),
  :deep(.data-category .card-list img),
  :deep(.data-tools .card-list img) {
    width: 36px !important;
    height: 36px !important;
  }
  /* 统计卡片紧凑化 */
  :deep(.t-card) {
    width: 100% !important;
    margin: 0 auto 10px auto !important;
    padding: 12px 6px 10px 6px !important;
    border-radius: 8px !important;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03) !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 90px !important;
  }
  :deep(.t-card__icon) {
    width: 28px !important;
    height: 28px !important;
    margin-bottom: 6px !important;
  }
  :deep(.t-card__title) {
    font-size: 13px !important;
    color: #374151 !important;
    margin-bottom: 2px !important;
    text-align: center !important;
    font-weight: 500 !important;
  }
  :deep(.t-card__value) {
    font-size: 18px !important;
    font-weight: bold !important;
    color: #2196f3 !important;
    margin-bottom: 0 !important;
    text-align: center !important;
  }
  :deep(.t-card__unit) {
    font-size: 12px !important;
    color: #6b7280 !important;
    margin-left: 2px !important;
  }
}
</style>
