<script setup lang="ts">
import { FileIcon, ImageIcon, UploadIcon } from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Divider,
  Form,
  FormItem,
  Loading,
  MessagePlugin,
  Radio,
  RadioGroup,
  Space,
  Textarea,
  Upload,
} from 'tdesign-vue-next';
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { alignKnowledge, extractKnowledge } from './api';

// 路由
const router = useRouter();

// 响应式数据
const loading = ref(false);
const extractType = ref('text');
const modelType = ref('2');
const lang = ref('cn');
const domainType = ref('common');
const content = ref('');
const files = ref<File | null>(null);
const entities = ref<any[]>([]);
const relations = ref<any[]>([]);
const isAlignVisible = ref(false);
const textJson = ref<File | null>(null);
const imageJson = ref<File | null>(null);
const uploadedFiles = ref<File[]>([]);
const uploadedImgs = ref<File[]>([]);

// 表单引用
const formRef = ref();

// 抽取参数
const extractParams = reactive({
  extract_type: 'text',
  domain_type: 'common',
  model_type: '2',
  lang: 'cn',
  content: '',
});

// 监听抽取类型变化
const handleExtractTypeChange = (value: string) => {
  extractType.value = value;
  extractParams.extract_type = value;
  // 清空内容
  content.value = '';
  files.value = null;
};

// 监听模型类型变化
const handleModelTypeChange = (value: string) => {
  modelType.value = value;
  extractParams.model_type = value;
};

// 监听语言类型变化
const handleLangChange = (value: string) => {
  lang.value = value;
  extractParams.lang = value;
};

// 监听领域类型变化
const handleDomainTypeChange = (value: string) => {
  domainType.value = value;
  extractParams.domain_type = value;
};

// 文件上传前处理
const beforeUpload = (file: File) => {
  const isLt500M = file.size / 1024 / 1024 < 500;
  if (!isLt500M) {
    MessagePlugin.error('上传文件大小不能超过 500MB!');
    return false;
  }

  if (extractType.value === 'image') {
    files.value = file;
    MessagePlugin.success('文件上传成功！');
  }
  return false; // 阻止自动上传
};

// 文本JSON文件上传
const beforeTextJsonUpload = (file: File) => {
  const isLt500M = file.size / 1024 / 1024 < 500;
  if (!isLt500M) {
    MessagePlugin.error('上传文件大小不能超过 500MB!');
    return false;
  }

  uploadedFiles.value.push(file);
  textJson.value = file;
  MessagePlugin.success('文本JSON文件上传成功！');
  return false;
};

// 图像JSON文件上传
const beforeImageJsonUpload = (file: File) => {
  const isLt500M = file.size / 1024 / 1024 < 500;
  if (!isLt500M) {
    MessagePlugin.error('上传文件大小不能超过 500MB!');
    return false;
  }

  uploadedImgs.value.push(file);
  imageJson.value = file;
  MessagePlugin.success('图像JSON文件上传成功！');
  return false;
};

// 抽取功能
const handleExtract = async () => {
  if (extractType.value === 'text' && !content.value.trim()) {
    MessagePlugin.warning('请输入文本内容');
    return;
  }

  if (extractType.value === 'image' && !files.value) {
    MessagePlugin.warning('请上传图像文件');
    return;
  }

  loading.value = true;

  try {
    const formData = new FormData();
    // 添加文件
    if (files.value) {
      formData.append('files', files.value);
    }
    // 添加参数
    const params = {
      extract_type: extractType.value,
      domain_type: domainType.value,
      model_type: Number(modelType.value) || '',
      lang: lang.value,
      content: content.value,
    };
    formData.append('data', JSON.stringify(params));

    // 调用抽取接口
    const response: any = await extractKnowledge(formData);

    if (response.code === 200) {
      MessagePlugin.success('抽取成功！');
      entities.value = response.data.result.entities || [];
      relations.value = response.data.result.relations || [];
      isAlignVisible.value = true;

      // 如果有下载链接，自动跳转下载
      if (response.data.download_url) {
        window.location.href = response.data.download_url;
        MessagePlugin.success('下载成功！');
      }
    } else {
      MessagePlugin.error(response.message || '抽取失败');
    }
  } catch (error) {
    console.error('抽取失败:', error);
    MessagePlugin.error('抽取失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 对齐功能
const handleAlign = async () => {
  if (!textJson.value || !imageJson.value) {
    MessagePlugin.error('文本和图像必须上传JSON文件才可以对齐！');
    return;
  }

  loading.value = true;

  try {
    const formData = new FormData();
    formData.append('text_json', textJson.value);
    formData.append('image_json', imageJson.value);

    const response: any = await alignKnowledge(formData);

    if (response.code === 200) {
      MessagePlugin.success('对齐成功！');

      // 处理对齐结果
      const imgDataJson = response.data.image_map || [];
      if (imgDataJson.length > 0) {
        imgDataJson.forEach((item: any) => {
          item.image_url = `/api/view/${item.image_url}`;
        });

        // 存储到localStorage并跳转
        localStorage.setItem('jsonData', JSON.stringify(imgDataJson));
        // 跳转到结果展示页面
        router.push('/relationship-result');
      }

      entities.value = response.data.result.entities || [];
      relations.value = response.data.result.relations || [];
    } else {
      MessagePlugin.error(response.message || '对齐失败');
    }
  } catch (error) {
    console.error('对齐失败:', error);
    MessagePlugin.error('对齐失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 文件大小格式化
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
};

// 重置表单
const resetForm = () => {
  content.value = '';
  files.value = null;
  entities.value = [];
  relations.value = [];
  isAlignVisible.value = false;
  textJson.value = null;
  imageJson.value = null;
  uploadedFiles.value = [];
  uploadedImgs.value = [];
};
</script>

<template>
  <div class="relationship-container">
    <Card class="main-card">
      <template #header>
        <div class="card-header">
          <h2 class="page-title">知识对象及关系挖掘工具</h2>
        </div>
      </template>

      <div class="description">
        <p class="description-text">
          化工领域知识对象抽取是针对化工科技文献中的文本、图像、表格等多源信息，精准抽取实体及实体之间关系的关键技术。
          其功能设计需深度适配化工领域的专业性、复杂性与多模态特性，知识对象抽取功能可实现从非结构化数据到可复用知识资产的转化，
          为研发、生产、安全等环节提供智能化支持，推动化工行业向数据驱动的新型研发范式转型。
        </p>
      </div>

      <Loading :loading="loading" show-overlay prevent-scroll-through>
        <div class="content-area">
          <div class="left-panel">
            <Form ref="formRef" label-align="top" class="extract-form">
              <FormItem label="文献类型：">
                <RadioGroup
                  v-model="extractType"
                  @change="handleExtractTypeChange"
                >
                  <Radio value="text">文本</Radio>
                  <Radio value="image">
                    <template #icon>
                      <ImageIcon />
                    </template>
                    图像
                  </Radio>
                </RadioGroup>
              </FormItem>

              <FormItem label="选择模型：">
                <RadioGroup v-model="modelType" @change="handleModelTypeChange">
                  <Radio value="1">本地模型</Radio>
                  <Radio value="2">deepseek</Radio>
                </RadioGroup>
              </FormItem>

              <FormItem label="选择语言类型：">
                <RadioGroup v-model="lang" @change="handleLangChange">
                  <Radio value="cn">中文</Radio>
                  <Radio value="en">英文</Radio>
                </RadioGroup>
              </FormItem>

              <FormItem label="选择领域：">
                <RadioGroup
                  v-model="domainType"
                  @change="handleDomainTypeChange"
                >
                  <Radio value="common">通用</Radio>
                  <Radio value="chemical">化工</Radio>
                  <Radio value="gene">基因</Radio>
                </RadioGroup>
              </FormItem>

              <FormItem label="输入内容：" required>
                <div class="input-container">
                  <Textarea
                    v-if="extractType === 'text'"
                    v-model="content"
                    placeholder="请输入要抽取的文本内容..."
                    :autosize="{ minRows: 10, maxRows: 15 }"
                    class="text-input"
                  />

                  <Upload
                    v-else
                    theme="custom"
                    draggable
                    :before-upload="beforeUpload"
                    action="/rgdc-sys/file/upload"
                    accept=".jpg,.jpeg,.png,.gif,.bmp,.webp"
                    class="upload-area"
                  >
                    <template #dragContent>
                      <div class="upload-trigger">
                        <UploadIcon size="48" />
                        <span class="upload-text">
                          将图像文件拖到此处，或点击上传
                        </span>
                      </div>
                    </template>
                  </Upload>

                  <!-- 文件信息显示 -->
                  <div v-if="files" class="file-info-display">
                    <div class="file-item">
                      <div class="file-info">
                        <span class="file-name">{{ files.name }}</span>
                        <span class="file-size">{{
                          formatFileSize(files.size)
                        }}</span>
                      </div>
                      <Button
                        size="small"
                        theme="danger"
                        variant="text"
                        @click="files = null"
                      >
                        删除
                      </Button>
                    </div>
                  </div>
                </div>
              </FormItem>

              <FormItem>
                <Space>
                  <Button
                    theme="primary"
                    @click="handleExtract"
                    :loading="loading"
                  >
                    开始抽取
                  </Button>
                  <Button theme="default" @click="resetForm"> 重置 </Button>
                </Space>
              </FormItem>
            </Form>
          </div>

          <div class="right-panel">
            <!-- 实体显示区域 -->
            <div v-if="entities.length > 0" class="result-section">
              <h4 class="section-title">抽取实体</h4>
              <div class="result-scrollbar">
                <div class="result-content">
                  <div
                    v-for="(item, index) in entities"
                    :key="index"
                    class="result-item"
                    v-html="item.value"
                  ></div>
                </div>
              </div>
            </div>

            <!-- 关系显示区域 -->
            <div v-if="relations.length > 0" class="result-section">
              <h4 class="section-title">抽取关系</h4>
              <div class="result-scrollbar">
                <div class="result-content">
                  <div
                    v-for="(item, index) in relations"
                    :key="index"
                    class="result-item"
                    v-html="item.value"
                  ></div>
                </div>
              </div>
            </div>

            <!-- 对齐功能区域 -->
            <div v-if="isAlignVisible" class="align-section">
              <Divider />
              <h4 class="section-title">跨模态对齐</h4>
              <p class="align-description">
                上传文本和图像的JSON文件进行跨模态对齐匹配
              </p>

              <div class="align-uploads">
                <Space direction="vertical" size="large">
                  <Upload
                    theme="file"
                    :before-upload="beforeTextJsonUpload"
                    :show-upload-progress="false"
                    accept=".json"
                    class="json-upload"
                  >
                    <template #trigger>
                      <Button theme="success" variant="outline">
                        <template #icon>
                          <FileIcon />
                        </template>
                        上传文本JSON
                      </Button>
                    </template>
                  </Upload>

                  <Upload
                    theme="file"
                    :before-upload="beforeImageJsonUpload"
                    :show-upload-progress="false"
                    accept=".json"
                    class="json-upload"
                  >
                    <template #trigger>
                      <Button theme="success" variant="outline">
                        <template #icon>
                          <FileIcon />
                        </template>
                        上传图像JSON
                      </Button>
                    </template>
                  </Upload>

                  <Button
                    theme="primary"
                    @click="handleAlign"
                    :loading="loading"
                    :disabled="!textJson || !imageJson"
                  >
                    开始对齐
                  </Button>
                </Space>
              </div>
            </div>
          </div>
        </div>
      </Loading>
    </Card>
  </div>
</template>

<style scoped lang="less">
.relationship-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.main-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.description {
  margin-bottom: 32px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #0052d9;
}

.description-text {
  margin: 0;
  line-height: 1.6;
  color: #333;
}

.content-area {
  display: flex;
  gap: 32px;
  min-height: 600px;
}

.left-panel {
  flex: 3;
  min-width: 600px;
  max-width: 900px;
}

.right-panel {
  flex: 1;
  min-width: 300px;
}

.extract-form {
  .t-form__item {
    margin-bottom: 24px;
  }
}

.input-container {
  width: 100%;
}

.text-input {
  width: 100%;
  min-height: 250px;
  resize: vertical;
}

.upload-area {
  width: 100%;
  position: relative;
  z-index: 1;

  // 隐藏文件列表区域，只保留拖拽区域
  :deep(.t-upload__flow-list) {
    display: none;
  }

  // 确保拖拽区域占满整个空间
  :deep(.t-upload__dragger) {
    width: 100%;
    height: 100%;
    border: none;
    background: transparent;
    padding: 0;
  }
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  border: 2px dashed #dcdcdc;
  border-radius: 12px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 200px;
  text-align: center;

  &:hover {
    border-color: #0052d9;
    background: #f0f7ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 82, 217, 0.1);
  }

  .upload-text {
    margin-top: 20px;
    color: #333;
    font-size: 16px;
    font-weight: 500;
  }

  .upload-tip {
    margin-top: 12px;
    color: #666;
    font-size: 14px;
  }
}

.file-info-display {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.file-size {
  color: #666;
  font-size: 12px;
}

.result-section {
  margin-bottom: 24px;

  .section-title {
    margin-bottom: 16px;
    color: #333;
    font-weight: 600;
    font-size: 18px;
  }
}

.result-scrollbar {
  height: 200px;
  border: 1px solid #e7e7e7;
  border-radius: 6px;
  background: #f8f9fa;
  overflow-y: auto;
  overflow-x: hidden;
}

.result-content {
  padding: 16px;
}

.result-item {
  padding: 8px 12px;
  margin-bottom: 8px;
  background: #fff;
  border-radius: 4px;
  border-left: 3px solid #0052d9;
  font-size: 14px;
  line-height: 1.5;

  &:last-child {
    margin-bottom: 0;
  }
}

.align-section {
  padding-top: 24px;

  .section-title {
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
    font-size: 18px;
  }

  .align-description {
    display: block;
    margin-bottom: 24px;
    color: #666;
    font-size: 14px;
  }
}

.align-uploads {
  .json-upload {
    width: 100%;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .content-area {
    flex-direction: column;
    gap: 24px;
  }

  .left-panel {
    flex: none;
    min-width: auto;
    max-width: none;
  }

  .right-panel {
    flex: none;
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .relationship-container {
    padding: 16px;
  }

  .content-area {
    gap: 20px;
  }

  .left-panel,
  .right-panel {
    min-width: auto;
  }

  .upload-trigger {
    padding: 24px 16px;
  }
}

// 滚动条样式
.result-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.result-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.result-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.result-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
