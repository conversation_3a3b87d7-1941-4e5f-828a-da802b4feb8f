<script setup lang="ts">
import {
  <PERSON><PERSON>, <PERSON>,
} from 'tdesign-vue-next';
import { computed, onMounted, reactive, ref } from 'vue';
import {BulletpointIcon} from 'tdesign-icons-vue-next';
import {getReferencesData} from '../api';
import { useSearchStore } from '#/store/search';

interface References {
  id: string;
  createdBy: string;
  createTime: string;
  updatedBy: string;
  updateTime: string;
  isDeleted: string;
  iupacStandardInchiKey: string;
  casRegistryNumber: string;
  desiredUnits: string;
  detail: string;
  refId: string;
  refUrl: string;
  title: string;
  isMore: Boolean;
}

const state: any = reactive({
  detailItem: {},
  loading: true,

});
// 响应式数据
const referenceList = ref<References[][]>([]);
// 控制显示全部的状态
const showAll = ref(false);

// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    console.log("当前详情数据", detail, 2222);
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    console.log("当前详情数据", local, 2222);
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

const initReference = async (flag: Boolean) => {
  try {
    // 设置显示全部状态,隐藏更多按钮
    showAll.value = flag;
    // 调用反应信息查询API
    const response = await getReferencesData({ iupacStandardInchiKey: state.detailItem.baseCode, isMore: flag });
    referenceList.value = response;
  } catch (error) {
    console.error('获取参考信息失败:', error);
  } finally {
    // 关闭加载状态
    state.loading = false;
  }
};

onMounted(async () => {
  await initReference(false);
});
</script>

<template>
  <div class="substance-react-info">
    <h1 class="title-style"><BulletpointIcon style="padding-right: 10px" size="1.4em"/>References</h1>
    <div v-if="state.loading" class="loading-container">
      <div class="loading-spinner">加载反应信息中...</div>
    </div>
    <template v-else>
      <div class="react-section">
        <div v-for="(reference, index) in referenceList" class="react-table-container">
          <h1 class="font-style">{{ reference.title }}</h1>
          <div class="table-content">
            <h3 class="font-style2">{{ reference.detail }}</h3>
<!--            <Link theme="primary">[all data]</Link>-->
          </div>
        </div>
        <div v-if="!showAll" class="show-more-container">
          <Button theme="primary" @click="initReference(true)">
            查看更多
          </Button>
        </div>
        <div v-if="referenceList.length === 0" class="empty-state">
          <p>暂无参考信息</p>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
.substance-react-info {
  padding: 20px;

  // 加载状态样式
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;

    .loading-spinner {
      font-size: 16px;
      color: #666;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        right: -20px;
        top: 50%;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        border: 2px solid #e8e8e8;
        border-top: 2px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }

    @keyframes spin {
      0% {
        transform: translateY(-50%) rotate(0deg);
      }

      100% {
        transform: translateY(-50%) rotate(360deg);
      }
    }
  }

  .title-style {
    font-size: 30px;
    font-weight: bold;
    padding-bottom: 50px;
  }

  .font-style {
    font-size: 20px;
    font-weight: bold;
  }

  .font-style2 {
    font-size: 15px;
    color: rgba(108, 108, 108, 1);
    padding: 10px 0 5px 0;
  }

  .react-table-container {
    margin-bottom: 5px;
    overflow: hidden;
    padding: 0px 0px 15px 20px;
  }

  .table-content {
    display: flex;
    padding: 10px 0px;
  }

  .show-more-container {
    display: flex;
    justify-content: center;
    margin-top: 16px;
  }
}

.dialog-content-scrollable {
  min-height: 50vh;
  max-height: 50vh;
  overflow-y: auto; // 垂直滚动
  padding-right: 8px; // 避免滚动条遮挡内容

  .step-item,
  .reference-item {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    p {
      margin: 8px 0;
      /* 调整段落间距 */
      line-height: 1.6;
      /* 增加行高 */
    }

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
  }

  .title-text {
    font-weight: 600;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f8f8f8;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #b8b8b8;
    border-radius: 10px;
    border: 1px solid #f8f8f8;

    &:hover {
      background-color: #909090;
    }
  }
}
</style>
