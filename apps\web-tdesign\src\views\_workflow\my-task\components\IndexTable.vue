<script lang="ts" setup>
import {MessagePlugin, type PageInfo, Select, type TableRowData} from 'tdesign-vue-next';

import ColumnDisplay from '#/components/column-display/index.vue';
import { BaseTableConfig, Pagination } from '#/utils/constant.ts';
import { removalUnderline } from '#/utils/sort';
import {Icon, RefreshIcon, SearchIcon} from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Col,
  Form,
  FormItem,
  Input,
  Link,
  Popconfirm,
  Row,
  Space,
  Table,
  Textarea,
  Dialog,
} from 'tdesign-vue-next';
import { defineEmits, defineExpose, defineProps, onMounted, ref } from 'vue';

import {listByPageApi, myTaskProcssList, rejectTask} from '../api';
import PutAway from "#/components/put-away/index.vue";

const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },
  cfgEditFormRef: { type: Object, default: null },
  dataDetailsViewRef: { type: Object, default: null },
});

const emit = defineEmits(['edit', 'add', 'claimTask', 'remove']);
/**
 * 表格定义
 */
const columns: any = ref([
  // {
  //   title: '选中标志',
  //   colKey: 'row-select',
  //   type: 'multiple',
  //   width: 64,
  // },
  {
    title: '序号',
    colKey: 'serial-number',
    width: 100,
  },
  {
    colKey: 'arg.operation_name',
    title: '数据名称',
  },
  {
    colKey: 'arg.operation_version',
    title: '数据版本',
    ellipsis: true,
  },
  {
    colKey: 'processName',
    title: '任务类型',
  },
  {
    colKey: 'createBy',
    title: '提交人',
    ellipsis: true,
  },

  {
    colKey: 'createTime',
    title: '提交时间',
    ellipsis: true,
    sorter: true,
  },
  {
    colKey: 'op',
    title: '操作',
    width: 200,
    fixed: 'center',
  },
]);
const displayColumns = ref<any['displayColumns']>(
  columns?.value.map((item: any) => item.colKey),
);
const formData: any = ref({});
const form = ref();
const data: any = ref([]);
const selectedRowKeys = ref([]);
const tableConfig = ref(BaseTableConfig);
const pagination: any = ref(Pagination);
const loading = ref(false);
const sort = ref([]);
/** -----------------------------------------------  */

/**
 * 网络请求调用定义
 */
const reqRunner = {
  listByPageApi: async (params: any) => {
    loading.value = true;
    try {
      const { records, total } = await listByPageApi(params);
      for(const item of records){
        item.arg = JSON.parse(item.variable)
        data.value.push(item)
      }
      // data.value = records;
      pagination.value = {
        ...pagination.value,
        total,
      };
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  },
};

/**
 * table初始化方法
 */
const loadData = async () => {
  data.value = []
  const params = {
    param: formData.value,
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    sorts: removalUnderline(sort.value),
  };
  await reqRunner.listByPageApi(params);
};
/**
 * 分页栏点击/更改响应方法
 * @param pageInfo
 * @param newDataSource
 */
const rehandlePageChange = (
  pageInfo: PageInfo,
  newDataSource: TableRowData[],
) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  loadData();
};

/**
 * 列排序点击响应时间
 * @param val
 */
const sortChange = (val: any) => {
  sort.value = val;
  // Request: 发起远程请求进行排序
  loadData();
};
/**
 * 搜索表单重置方法
 */
const onReset = () => {
  pagination.value = {
    pageSize: pagination.value.pageSize || pagination.value.pageSize,
    current: 1,
  };
  formData.value = {};
  form.value.reset();
  setTimeout(() => {
    loadData();
  }, 0);
};
/**
 * 搜索表单提交方法
 */
const onSubmit = async () => {
  pagination.value = {
    pageSize: pagination.value.pageSize || pagination.value.pageSize,
    current: 1,
  };
  setTimeout(() => {
    loadData();
  }, 0);
};

/**
 * 行选中响应时间
 * @param value
 * @param ctx
 */
const rehandleSelectChange = (value: any) => {
  selectedRowKeys.value = value;
};

/**
 * 列拖动交换位置响应
 * @param newData
 * @param sort
 */
const onDragSort = ({ newData, sort }: any) => {
  if (sort === 'col') {
    columns.value = newData;
  }
};

const processKeyList = ref()
onMounted(async () => {
  loadData();
  processKeyList.value = await myTaskProcssList();
});

/**
 * 编辑按钮响应
 * @param record
 */
const edit = (record: any) => {
  emit('edit', record);
};
const view = (record: any) => {
  let data = record
  data.templateCode = record.arg.template_code
  data.baseCode = record.arg.operation_code
  data.rejectReason = record.arg.rejectReason
  data.classCode = record.arg.class_code
  data.categoryCode = record.arg.category_code
  data.labelCode = record.arg.label_code
  data.datasetCode = record.arg.dataset_code
  data.isReadonly = true
  props.cfgEditFormRef?.open(data ? { ...data, isedit: false } : {});
};
const viewDataAuth = (record: any) => {
  let data = record
  if (record.arg.resource_type === '**unstructured_file**'){
    data.baseCode = record.arg.operation_code
    data.rejectReason = record.arg.rejectReason
    data.startTime = record.arg.start_time
    data.endTime = record.arg.end_time
    data.permissionType = record.arg.permission_type
    data.remark = record.arg.remark
    data.fileType = record.arg.file_type
    data.classCode = record.arg.class_code
    data.categoryCode = record.arg.category_code
    data.labelCode = record.arg.label_code
    props.dataDetailsViewRef?.open(data ? { ...data, isedit: false, isfile: true } : {});
  } else {
    data.templateCode = record.arg.template_code
    data.baseCode = record.arg.operation_code
    data.rejectReason = record.arg.rejectReason
    data.classCode = record.arg.class_code
    data.categoryCode = record.arg.category_code
    data.labelCode = record.arg.label_code
    data.datasetCode = record.arg.dataset_code
    data.startTime = record.arg.start_time
    data.endTime = record.arg.end_time
    data.permissionType = record.arg.permission_type
    data.remark = record.arg.remark
    data.isReadonly = true
    data.metadataCodeList = record.arg.metadataCodeList
    props.dataDetailsViewRef?.open(data ? { ...data, isedit: false, isfile: false  } : {});
  }

};

const claimTask = (record: any) => {
  emit('claimTask', record);
};

/**
 * 行点击时间
 */
const handleRowClick = () => {};

/**
 * 单行删除按钮响应
 * @param row
 */
const remove = async (record: any) => {
  emit('remove', record);
};

const refresh = () => {
  loadData();
};
const handleConfirmRejectDialog = () => {
  if(rejectReason.value.trim() == ''){
    MessagePlugin.warning('请输入驳回原因');
    return
  }else{
    rejectRow.value.arg.rejectReason = rejectReason.value
    rejectDialog.value = false
    remove(rejectRow.value)
  }
};
const handleCloseRejectDialog = () => {
  rejectDialog.value = false
};
const reject = (row) => {
  rejectDialog.value = true
  rejectRow.value = row
};
/**
 * 方法/属性导出
 */
defineExpose({
  loadData,
  refresh,
});
const rejectDialog = ref(false)
const rejectReason = ref('')
const rejectRow = ref()
</script>
<template>
  <Dialog v-if="rejectDialog" header="确认驳回么" theme="warning" cancel-btn="取消" confirm-btn="确认"
    :visible="rejectDialog" @confirm="handleConfirmRejectDialog"  @cancel="handleCloseRejectDialog"
          :closeBtn="false">
    <div style="padding-top: 20px;">
      <Textarea v-model="rejectReason" placeholder="请输入驳回原因" maxlength="200"/>
    </div>
  </Dialog>
  <Space class="w-full" direction="vertical" size="small">
    <Card>
      <Form ref="form" :data="formData" :label-width="80" @reset="onReset" @submit="onSubmit">
        <div class="grid w-full grid-cols-3 gap-1 p-3">
          <FormItem label="数据名称" name="dataName">
            <Input v-model="formData.dataName" clearable placeholder="请输入"/>
          </FormItem>
          <FormItem label="任务类型" name="processKey">
            <Select v-model="formData.processKey" :options="processKeyList" clearable placeholder="请选择"/>
          </FormItem>
          <FormItem label="提交人" name="createBy">
            <Input v-model="formData.createBy" clearable placeholder="请输入"/>
          </FormItem>
        </div>
        <div class="mt-2 flex items-center justify-end space-x-2">
          <Button theme="primary" type="submit">
            <template #icon>
              <SearchIcon />
            </template>
            查询
          </Button>
          <Button theme="default" type="reset">
            <template #icon>
              <RefreshIcon />
            </template>
            重置
          </Button>
        </div>
      </Form>
    </Card>
    <Card>
      <div class="t-row--space-between mb-2 flex w-full justify-start">
        <div class="flex flex-wrap w-full items-center justify-start gap-1">
          <div class="t-card__title ml-2">我的审批列表</div>
          <div v-if="selectedRowKeys && selectedRowKeys.length > 0" class="text-[gray]">
            已选择 {{ selectedRowKeys?.length || 0 }} 条数据
          </div>
        </div>
        <div class="flex flex-wrap w-full justify-end gap-2">
          <ColumnDisplay v-model="tableConfig.columnControllerVisible" />
        </div>
      </div>
      <Table
        v-model:column-controller-visible="tableConfig.columnControllerVisible"
        v-model:display-columns="displayColumns"
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="pagination"
        :pagination-affixed-bottom="true"
        :selected-row-keys="selectedRowKeys"
        :sort="sort"
        v-bind="tableConfig"
        @drag-sort="onDragSort"
        @page-change="rehandlePageChange"
        @row-click="handleRowClick"
        @select-change="rehandleSelectChange"
        @sort-change="sortChange"
      >
        <template #op="{ row }">
          <Space size="small">
            <Link v-if="row.actorType !== 0" theme="primary" @click="claimTask(row)">
              认领
            </Link>
            <Link v-if="row.actorType === 0 && row.arg.operation_type !== 'dataAuthApply'" theme="primary" @click="view(row)">
              查看
            </Link>
            <Link v-if="row.actorType === 0 && row.arg.operation_type === 'dataAuthApply'" theme="primary" @click="viewDataAuth(row)">
              查看
            </Link>
            <Popconfirm v-if="row.actorType === 0" content="是否审核通过？" theme="default" @confirm="edit(row)">
              <Link theme="primary"> 通过 </Link>
            </Popconfirm>
            <Link v-if="row.actorType === 0" theme="danger" @click="reject(row)">
               驳回
            </Link>
          </Space>
        </template>
      </Table>
    </Card>
  </Space>
</template>
