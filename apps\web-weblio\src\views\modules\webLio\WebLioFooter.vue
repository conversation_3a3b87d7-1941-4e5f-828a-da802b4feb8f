<script setup lang="ts">
import { requestClient } from '#/api/request';
import { onMounted, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { useRouter } from 'vue-router';

const router = useRouter();

// 联系方式数据
interface ContactMethod {
  id: number | string;
  type: string;
  method: string;
  value: string;
  iconPath: string;
}
const contactMethods = ref<ContactMethod[]>([]);

// 二维码图片URL
const qrCodeUrl = ref('');
const qrText = ref('');
const infoTitle = ref('');
const infoDescription = ref('');

function getPageFoot() {
  return requestClient.post<any[]>('/rgdc-search/tHomePage/getPageFoot');
}

const initFooterData = async () => {
  // const res = await getPageFoot({});
  useRequest(getPageFoot, {
    onSuccess: (res) => {
      const { phoneList, qrcodeList, introductionList }: any = res;

      // 设置二维码图片URL
      if (qrcodeList && qrcodeList.length > 0) {
        qrCodeUrl.value = qrcodeList[0].qrCodeUrl || qrcodeList[0].url || '';
        qrText.value = qrcodeList[0].officialAccountText || '';
      }
      if (introductionList && introductionList.length > 0) {
        infoTitle.value = introductionList[0].descriTitle || '';
        infoDescription.value = introductionList[0].descriText || '';
      }
      const tempIcon =
        'M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z';
      contactMethods.value = phoneList.map((item) => {
        return {
          id: item.id,
          type: item.contactNumb,
          method: 'phone',
          value: item.contactNumb,
          iconPath: tempIcon,
        };
      });
    },
  });
};

// 方法
const handleContact = async (contact: any) => {
  try {
    // 使用现代浏览器的剪切板API复制文本
    await navigator.clipboard.writeText(contact.value);

    // 显示成功提示
    // 这里可以使用你项目中的消息提示组件
    // 暂时使用原生alert，建议替换为项目中的toast组件
    alert(`已复制到剪切板: ${contact.value}`);
  } catch {
    // 如果剪切板API不可用，使用传统方法
    fallbackCopyTextToClipboard(contact.value);
  }
};

// 备用的复制方法（兼容旧浏览器）
const fallbackCopyTextToClipboard = (text: string) => {
  const textArea = document.createElement('textarea');
  textArea.value = text;

  // 避免页面滚动
  textArea.style.top = '0';
  textArea.style.left = '0';
  textArea.style.position = 'fixed';
  textArea.style.opacity = '0';

  document.body.append(textArea);
  textArea.focus();
  textArea.select();

  try {
    const successful = document.execCommand('copy');
    if (successful) {
      alert(`已复制到剪切板: ${text}`);
    } else {
      alert('复制失败，请手动复制');
    }
  } catch {
    alert('复制失败，请手动复制');
  }

  textArea.remove();
};

const openFeedback = () => {
  router.push('/user-feedback');
};
onMounted(() => {
  initFooterData();
});
</script>

<template>
  <footer class="weblio-footer">
    <!-- 用户反馈按钮 -->
    <div class="feedback-button" @click="openFeedback">
      <div class="feedback-icon">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path
            d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 13h-2v-2h2v2zm0-4h-2V7h2v4z"
          />
        </svg>
      </div>
      <span>用户反馈</span>
    </div>

    <div class="footer-content">
      <div class="footer-container">
        <!-- 左侧机构信息 -->
        <div class="footer-left">
          <div class="institute-info">
            <div class="logo-section">
              <div class="logo">
                <img src="/static/images/02-Vector.png" alt="logo" />
              </div>
              <h2 class="institute-name">{{ infoTitle }}</h2>
            </div>
            <p class="disclaimer">
              {{ infoDescription }}
            </p>
          </div>
        </div>

        <!-- 联系方式 -->
        <div class="footer-contact">
          <div class="contact-section">
            <h3 class="section-title">联系方式</h3>
            <div class="contact-methods">
              <div
                v-for="contact in contactMethods"
                :key="contact.id"
                class="contact-item"
                @click="handleContact(contact)"
              >
                <div class="contact-icon">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path :d="contact.iconPath" />
                  </svg>
                </div>
                <span class="contact-text">{{ contact.type }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 公众号二维码 -->
        <div class="footer-qr">
          <div class="qr-section">
            <div class="qr-code">
              <img
                v-if="qrCodeUrl"
                :src="qrCodeUrl"
                alt="微信公众号二维码"
                class="qr-image"
              />
              <div v-else class="qr-placeholder">
                <div class="loading-text">加载中...</div>
              </div>
            </div>
            <p class="qr-text">{{ qrText }}</p>
          </div>
        </div>
      </div>

      <!-- 版权信息 -->
      <div class="footer-bottom">
        <p class="copyright">Copyright ©大连化学物理研究所</p>
      </div>
    </div>
  </footer>
</template>

<style scoped lang="scss">
.weblio-footer {
  position: relative;
  background: linear-gradient(135deg, #1e5fae 0%, #2a69c4 100%);
  color: white;
  padding: 10px 0 4px; // 维持压缩高度
}

.feedback-button {
  position: fixed;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: var(--td-brand-color-hover, #1976d2);
  color: white;
  padding: 12px 16px;
  border-radius: 25px 0 0 25px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  z-index: 1000;
  writing-mode: vertical-rl;
  text-orientation: mixed;

  &:hover {
    background: var(--td-brand-color-active, #1565c0);
    transform: translateY(-50%) translateX(-5px);
  }

  .feedback-icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  span {
    font-size: 14px;
    font-weight: 500;
  }
}

.footer-content {
  max-width: 700px;
  margin: 0 auto;
  padding: 0 6px;
}

.footer-container {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 12px;
  margin-bottom: 8px;
  align-items: flex-start;
  @media (max-width: 900px) {
    grid-template-columns: 1fr 1fr;
    .footer-qr {
      grid-column: 1 / -1;
      justify-self: center;
      margin-top: 8px;
    }
  }
  @media (max-width: 600px) {
    grid-template-columns: 1fr;
    gap: 6px;
  }
}

.footer-left {
  .institute-info {
    .logo-section {
      display: flex;
      align-items: center;
      gap: 10px; // 恢复
      margin-bottom: 10px; // 恢复
      .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        background: white;
        border-radius: 50%;
        width: 32px; // 恢复
        height: 32px; // 恢复
        img {
          height: 28px; // 恢复
          width: auto;
        }
      }
      .institute-name {
        font-size: 18px; // 恢复
        font-weight: bold;
        margin: 0;
        color: white;
      }
    }
    .disclaimer {
      line-height: 1.4; // 恢复
      font-size: 12px; // 恢复
      opacity: 0.9;
      margin: 0;
    }
  }
}

.footer-contact {
  .contact-section {
    .section-title {
      font-size: 15px; // 恢复
      font-weight: bold;
      margin-bottom: 10px; // 恢复
      color: white;
    }
    .contact-methods {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px; // 恢复
      .contact-item {
        display: flex;
        align-items: center;
        gap: 8px; // 恢复
        padding: 6px; // 恢复
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s ease;
        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }
        .contact-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px; // 恢复
          height: 24px; // 恢复
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
        }
        .contact-text {
          font-size: 12px; // 恢复
          color: white;
        }
      }
    }
  }
}

.footer-qr {
  .qr-section {
    text-align: center;
    .qr-code {
      background: white;
      border-radius: 8px;
      padding: 10px; // 恢复
      margin-bottom: 8px; // 恢复
      display: inline-block;
      .qr-image {
        width: 60px; // 恢复
        height: 60px; // 恢复
        object-fit: contain;
        border-radius: 4px;
      }
      .qr-placeholder {
        .loading-text {
          font-size: 12px; // 恢复
          margin: 0;
          opacity: 0.9;
          color: #666;
          width: 60px; // 恢复
          height: 60px; // 恢复
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
    .qr-text {
      font-size: 12px; // 恢复
      margin: 0;
      opacity: 0.9;
      color: white;
    }
  }
}

.footer-bottom {
  text-align: center;
  padding-top: 4px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  .copyright {
    margin: 0;
    font-size: 12px; // 恢复
    opacity: 0.8;
  }
}

// 用户反馈弹窗样式
.feedback-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.feedback-modal {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;

    h3 {
      margin: 0;
      color: #333;
      font-size: 18px;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        color: #333;
      }
    }
  }

  .modal-body {
    padding: 20px;

    .form-group {
      margin-bottom: 20px;

      label {
        display: block;
        margin-bottom: 8px;
        color: #333;
        font-weight: 500;
      }

      .form-select,
      .form-input,
      .form-textarea {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        transition: border-color 0.3s ease;

        &:focus {
          outline: none;
          border-color: var(--td-brand-color-hover, #1976d2);
        }
      }

      .form-textarea {
        resize: vertical;
        min-height: 80px;
      }
    }
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px;
    border-top: 1px solid #eee;

    .btn-cancel,
    .btn-submit {
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .btn-cancel {
      background: #f5f5f5;
      border: 1px solid #ddd;
      color: #666;

      &:hover {
        background: #e9e9e9;
      }
    }

    .btn-submit {
      background: var(--td-brand-color-hover, #1976d2);
      border: 1px solid var(--td-brand-color-hover, #1976d2);
      color: white;

      &:hover:not(:disabled) {
        background: var(--td-brand-color-active, #1565c0);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}
</style>
