<script setup lang="ts">
import { Card } from 'tdesign-vue-next';
import { onMounted, ref } from 'vue';

// 响应式数据
const jsonData = ref<any[]>([]);

// 组件挂载时从localStorage获取数据
onMounted(() => {
  const storedData = localStorage.getItem('jsonData');
  if (storedData) {
    try {
      jsonData.value = JSON.parse(storedData);
    } catch (error) {
      console.error('解析存储数据失败:', error);
    }
  }
});
</script>

<template>
  <div class="relationship-result-container">
    <Card class="main-card">
      <template #header>
        <div class="card-header">
          <h2 class="page-title">知识对象及关系挖掘工具 - 对齐结果</h2>
        </div>
      </template>

      <div class="description">
        <p class="description-text">
          以下是文本和图像跨模态对齐的结果展示，左侧为图像内容，右侧为对应的文本描述。
        </p>
      </div>

      <div class="result-content">
        <div
          v-for="(item, index) in jsonData"
          :key="index"
          class="journal-entry"
        >
          <img
            :src="item.image_url"
            :alt="`对齐结果 ${index + 1}`"
            class="journal-image"
          />
          <div class="journal-content">
            <div
              v-for="(textItem, textIndex) in item.text"
              :key="textIndex"
              class="text-item"
              v-html="textItem.value"
            ></div>
          </div>
        </div>

        <div v-if="jsonData.length === 0" class="empty-state">
          <p>暂无对齐结果数据</p>
        </div>
      </div>
    </Card>
  </div>
</template>

<style scoped lang="less">
.relationship-result-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.main-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.description {
  margin-bottom: 32px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #0052d9;
}

.description-text {
  margin: 0;
  line-height: 1.6;
  color: #333;
}

.result-content {
  padding: 20px 0;
}

.journal-entry {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
  padding: 20px;
  border-radius: 12px;
  background-color: #f8f9fa;
  transition: all 0.3s ease;

  &:hover {
    background-color: #ecf5ff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }
}

.journal-image {
  width: 300px;
  height: 300px;
  margin-right: 24px;
  object-fit: contain;
  border-radius: 8px;
  border: 1px solid #e7e7e7;
  background: #fff;
  flex-shrink: 0;
}

.journal-content {
  flex: 1;
  min-width: 0;
}

.text-item {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #fff;
  border-radius: 6px;
  border-left: 3px solid #0052d9;
  font-size: 14px;
  line-height: 1.6;
  color: #333;

  &:last-child {
    margin-bottom: 0;
  }

  :deep(p) {
    margin: 0;
    font-size: 16px;
    color: #02678d;
    font-weight: 600;

    strong {
      color: #000;
      font-weight: 700;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 16px;
}

// 响应式设计
@media (max-width: 768px) {
  .relationship-result-container {
    padding: 16px;
  }

  .journal-entry {
    flex-direction: column;
    padding: 16px;
  }

  .journal-image {
    width: 100%;
    height: 200px;
    margin-right: 0;
    margin-bottom: 16px;
  }

  .journal-content {
    width: 100%;
  }
}
</style>
