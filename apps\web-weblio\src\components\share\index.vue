<script setup lang="tsx">
import { ShareIcon } from 'tdesign-icons-vue-next';
import { Button } from 'tdesign-vue-next';

import { useShareDialog } from './useShareDialog';

const props = defineProps<{
  row: any;
}>();

const { getShareUrl, openShareDialog, ShareDialog } = useShareDialog();
async function handleShare() {
  const url = await getShareUrl(props?.row?.baseCode);
  openShareDialog(url);
}
</script>

<template>
  <Button
    shape="circle"
    theme="default"
    variant="text"
    @click.stop="handleShare()"
    class="share-btn"
  >
    <ShareIcon size="20" />
  </Button>
  <component :is="ShareDialog" />
</template>

<style scoped lang="less">
.share-btn {
  color: #00b8d9;
  &:hover {
    color: #36cfc9;
  }
}
</style>
