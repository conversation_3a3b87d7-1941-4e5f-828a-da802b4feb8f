import { requestClient } from '#/api/request';

export async function listByPageApi(data: any) {
  return requestClient.post<any>('/rgdc-workflow/queryTask/myTaskByPage2', data);
}

export async function executeTask(data: any) {
  return requestClient.post<any>('/rgdc-workflow/task/executeTask', data);
}

export async function claimTask(data: any) {
  return requestClient.post<any>(`/rgdc-workflow/task/claimTask`, data);
}

export async function rejectTask(data: any) {
  return requestClient.post<any>(`/rgdc-workflow/task/rejectTask`, data);
}
export async function myTaskProcssList() {
  return requestClient.get<any>(`/rgdc-workflow/queryTask/myTaskProcssList`);
}
