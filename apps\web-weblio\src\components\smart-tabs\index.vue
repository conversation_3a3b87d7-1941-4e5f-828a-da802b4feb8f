<script setup lang="ts">
import type { TabOption } from '../../store/tabs';

import { computed, useSlots, watch } from 'vue';

import { useTabsStore } from '../../store/tabs';

interface Props {
  options: TabOption[];
  modelValue?: string;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'change', value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  options: () => [],
});

const emit = defineEmits<Emits>();
const slots = useSlots();

const tabsStore = useTabsStore();

// 计算当前选中的tab
const activeTab = computed(() => {
  return props.modelValue || tabsStore.activeTab;
});

// 检查是否有默认插槽内容
const hasSlotContent = computed(() => {
  return !!slots.default;
});

const handleTabClick = (tab: TabOption) => {
  if (tab.disabled) return;

  tabsStore.setActiveTab(tab.key);
  emit('update:modelValue', tab.key);
  emit('change', tab.key);
};

// 监听选项变化，更新store
watch(
  () => props.options,
  (newOptions) => {
    tabsStore.setTabs(newOptions);
  },
  { immediate: true, deep: true },
);
</script>

<template>
  <div class="smart-tabs">
    <div class="smart-tabs__nav">
      <div class="smart-tabs__track">
        <div
          v-for="tab in options"
          :key="tab.key"
          class="smart-tabs__tab"
          :class="[
            {
              'smart-tabs__tab--active': activeTab === tab.key,
              'smart-tabs__tab--disabled': tab.disabled,
            },
          ]"
          @click="handleTabClick(tab)"
        >
          <i v-if="tab.icon" :class="tab.icon" class="smart-tabs__icon"></i>
          <span class="smart-tabs__label">{{ tab.label }}</span>
        </div>
      </div>
    </div>

    <!-- 内容区域 - 只在有插槽内容时才创建 -->
    <div v-if="hasSlotContent" class="smart-tabs__content">
      <slot :active-tab="activeTab"></slot>
    </div>
  </div>
</template>

<style lang="less" scoped>
.smart-tabs {
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  height: 100%;
  &__nav {
    position: relative;
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    padding: 0;
    overflow: hidden;
  }

  &__track {
    position: relative;
    display: flex;
    gap: 0;
    align-items: center;
    justify-content: center;
  }

  &__tab {
    position: relative;
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 16px 24px;
    cursor: pointer;
    background: transparent;
    color: #374151;
    font-size: 18px;
    font-weight: 500;
    transition: all 0.2s ease;
    z-index: 2;
    white-space: nowrap;
    border-bottom: 4px solid transparent;

    &:hover:not(&--disabled):not(&--active) {
      color: #1f2937;
      background-color: transparent;
    }

    &--active {
      color: #083786;
      border-bottom-color: #ffffff;
      background-color: transparent;

      .smart-tabs__icon {
        color: #083786;
      }
    }

    &--disabled {
      color: #9ca3af;
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  &__icon {
    font-size: 14px;
    transition: all 0.2s ease;
  }

  &__label {
    transition: all 0.2s ease;
    font-weight: 500;
  }

  &__glow {
    display: none; // 移除光晕效果
  }

  &__indicator {
    display: none; // 移除底部指示器，使用border-bottom代替
  }

  &__content {
    margin-top: 0;
    padding-top: 20px;
  }
}

// 深色主题适配
.dark .smart-tabs {
  &__nav {
    background: #1f2937;
    border-bottom-color: #374151;
  }

  &__tab {
    color: #d1d5db;

    &:hover:not(&--disabled):not(&--active) {
      color: #f3f4f6;
      background-color: transparent;
    }

    &--active {
      color: #083786;
      border-bottom-color: #ffffff;
      background-color: transparent;

      .smart-tabs__icon {
        color: #083786;
      }
    }

    &--disabled {
      color: #6b7280;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .smart-tabs {
    &__tab {
      padding: 12px 16px;
      font-size: 13px;
    }

    &__icon {
      font-size: 13px;
    }
  }
}
</style>
