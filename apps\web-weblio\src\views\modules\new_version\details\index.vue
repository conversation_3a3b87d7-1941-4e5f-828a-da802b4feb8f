<script setup lang="ts">
import { useSearchStore } from '#/store/search';
import { computed, ref, watch } from 'vue';

import { saveBrowsingHistory } from './api';
import BasicInfo from './modules/basic-info/index.vue';
import Dataset from './modules/dataset/index.vue';
import Document from './modules/document/index.vue';
import TimeSeries from './modules/time-series/index.vue';

const searchStore = useSearchStore();
const hasSavedBrowsingHistory = ref(false); // 标记是否已经保存过浏览记录

const detailItem = computed(() => {
  const detail = searchStore.currentDetailItem;
  if (detail) {
    return detail;
  } else {
    const local = localStorage.getItem('currentDetailItem');
    return JSON.parse(local ?? '{}');
  }
});

// 监听detailItem变化，第一次有值时保存浏览记录
watch(
  detailItem,
  async (newValue) => {
    // 如果有值且还没有保存过浏览记录
    if (
      newValue &&
      Object.keys(newValue).length > 0 &&
      !hasSavedBrowsingHistory.value
    ) {
      console.log('newValue', newValue);
      try {
        await saveBrowsingHistory({
          ...newValue,
          baseCode: newValue.baseCode,
          operationCode: newValue.baseCode,
          dataInformation:
            newValue?.dataInformation || JSON.stringify(newValue.infomation),
          createTime: null,
          createdBy: null,
          updateTime: null,
          updatedBy: null,
          id: null,
          // 可以根据需要添加更多参数
        });
        hasSavedBrowsingHistory.value = true;
        console.warn('浏览记录已保存');
      } catch (error) {
        console.error('保存浏览记录失败:', error);
      }
    }
  },
  { immediate: true }, // 立即执行一次
);

const type = detailItem.value?.dataType ?? '';
// 兼容：如果没有item，渲染空div

// type与组件的对应关系映射
const componentMap: Record<string, any> = {
  '0': BasicInfo, // 基础信息
  '1': BasicInfo, // 基础信息
  '2': BasicInfo, // 基础信息
  '3': Dataset, // 数据集
  '4': Document, // 文档
  '5': Document, // 文档
  '6': Document, // 文档
  '7': Document, // 文档
  '8': TimeSeries, // 时序类
};
</script>

<template>
  <!-- 根据type动态渲染对应组件，未匹配则渲染空div -->
  <component :is="componentMap[type] || 'div'" :item="detailItem" />
</template>

<style scoped lang="scss"></style>
