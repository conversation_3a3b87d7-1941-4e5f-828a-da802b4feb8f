import type { PropType } from 'vue';

import Collection from '#/components/collection/index.vue';
import Share from '#/components/share/index.vue';
import { LockOffIcon, LockOnIcon } from 'tdesign-icons-vue-next';
import { Tag as TTag, Tooltip as TTooltip } from 'tdesign-vue-next';
import { defineComponent } from 'vue';

import styles from './index.module.less';

/**
 * 文献数据-基本信息
 */
interface TopCardData {
    /**
     * 文献数据-基本信息的ID
     */
    id: number | null;
    /**
     * 作者
     */
    author: string | null;
    /**
     * DOI
     */
    doi: string | null;
    /**
     * 数据批次
     */
    batch: string | null;
    /**
     * 引用次数
     */
    citedTimes: number | null;
    /**
     * 分类
     */
    classification: string | null;
    /**
     * 创建时间，格式为 "yyyy-MM-dd HH:mm:ss"
     */
    createTime: string | null;
    /**
     * 创建人
     */
    createdBy: string | null;
    /**
     * 交付时间，格式为 "yyyy-MM-dd HH:mm:ss"
     */
    deliveryTime: string | null;
    /**
     * 摘要
     */
    docAbstract: string | null;
    /**
     * 文献类数据具体类型(0:论文,1:报告,2:专著,3:教材)
     */
    docType: string | null;
    /**
     * 电子期刊国际标准连续出版物号（eissn）
     */
    eissn: string | null;
    /**
     * 一级目录
     */
    firstDirectory: string | null;
    /**
     * 封面图像
     */
    img: string | null;
    /**
     * 删除标识(0未删除1删除)，不可为空
     */
    isDeleted: string;
    /**
     * MD解构目录是否为空0为空1不为空
     */
    isMd: string | null;
    /**
     * 期号（issn）
     */
    issue: string | null;
    /**
     * 关键词
     */
    keyword: string | null;
    /**
     * 文献出版信息
     */
    literature: string | null;
    /**
     * markdown输出目录
     */
    mdPath: string | null;
    /**
     * 页码
     */
    page: string | null;
    /**
     * PDF全文名称
     */
    pdfName: string | null;
    /**
     * PDF路径
     */
    pdfPath: string | null;
    /**
     * 期刊标题
     */
    publicationTitle: string | null;
    /**
     * 出版年
     */
    publicationYear: string | null;
    /**
     * 引用方式
     */
    referenceDataset: string | null;
    /**
     * 数据标识
     */
    sdid: number | null;
    /**
     * 来源
     */
    source: string | null;
    /**
     * 标题
     */
    title: string | null;
    /**
     * 唯一号
     */
    unique: string | null;
    /**
     * 更新时间，格式为 "yyyy-MM-dd HH:mm:ss"
     */
    updateTime: string | null;
    /**
     * 更新人
     */
    updatedBy: string | null;
    /**
     * 卷号
     */
    vol: string | null;
    /**
     * 版本号
     */
    version: string | null;
    /**
     * 数据类型
     */
    dataType: string | null;
    /**
     * 分类
     */
    classifyName: string | null;
    /**
     * 标签编码
     */
    labelCode: string | null;
    /**
     * 分级
     */
    dataLevelName: string | null;
    dataImg: string;
    dataAuth: string | null;
    browsingCount: any;
    isFavorite: boolean;
    extra?: string;
    actions?: any;
}


export default defineComponent({
  name: 'TopCard',
  props: {
    data: {
      type: Object as PropType<any>,
      required: true,
    },
  },
  setup(props) {
    const { data } = props;
    console.log('data', data);
    return () => (
      <div class={styles['top-card']}>
        <div class={styles['top-card-main']}>
          {/* 左侧图片区 */}
          <div class={styles['top-card-cover']}>
            <img
              src={data.value.dataImg}
              style="width:100%;height:100%;object-fit:cover;"
            />
            {/* 右上角公开/私有图标 */}
            {data.value.dataAuth && (
              <div class={styles['top-card-public-icon']}>
                <TTooltip
                  content={data.value.dataAuth}
                  placement="top"
                >
                  {data.value.dataAuth === '公开' ? (
                    <LockOffIcon size={'20px'} style={{ color: '#52c41a' }} />
                  ) : (
                    <LockOnIcon size={'20px'} style={{ color: '#faad14' }} />
                  )}
                </TTooltip>
              </div>
            )}
          </div>
          {/* 右侧信息区 */}
          <div class={styles['top-card-info']}>
            <div class={styles['top-card-title-row']}>
              <div class={styles['top-card-title']}>
                {data.value.title}
              </div>
              {data.value.extra && (
                <div class={styles['top-card-extra']}>{data.value.extra}</div>
              )}
            </div>
            <div class={styles['top-card-info-list']}>
              <div class={styles['top-card-info-item']}>
                <span class={styles['top-card-info-label']}>DOI</span>
                <span class={styles['top-card-info-value']}>{data.value.doi}</span>
              </div>
              <div class={styles['top-card-info-item']}>
                <span class={styles['top-card-info-label']}>作者</span>
                <span class={styles['top-card-info-value']}>
                  {data.value.author}
                </span>
              </div>
              <div class={styles['top-card-info-item']}>
                <span class={styles['top-card-info-label']}>资源类型</span>
                <span class={styles['top-card-info-value']}>
                  {data.value.dataType}
                </span>
              </div>
              <div class={styles['top-card-info-item']}>
                <span class={styles['top-card-info-label']}>发布时间</span>
                <span class={styles['top-card-info-value']}>
                  {data.value.deliveryTime}
                </span>
              </div>
              <div class={styles['top-card-info-item']}>
                <span class={styles['top-card-info-label']}>关键词</span>
                <span class={styles['top-card-info-value']}>
                  {data.value.keyword &&
                  typeof data.value.keyword === 'string' &&
                  data.value.keyword.trim() !== ''
                    ? data.value.keyword
                        .split(',')
                        .map((kw: string, idx: number) => (
                          <TTag
                            key={idx}
                            shape="round"
                            style="margin: 0 6px 4px 0;"
                            theme="primary"
                            variant="outline"
                          >
                            {kw.trim()}
                          </TTag>
                        ))
                    : '-'}
                </span>
              </div>
            </div>
            {/* 操作按钮区 */}
            <div class={styles['top-card-actions']}>
              <span class={styles['top-card-action-item']}>
                浏览：{String(data.value.browsingCount ?? 0)}
              </span>
              <Collection isFavorite={data.value.isFavorite} row={data.value} />
              <Share row={data.value} />
              {data.value.actions &&
                (typeof data.value.actions === 'function'
                  ? data.value.actions()
                  : data.value.actions)}
            </div>
          </div>
        </div>
      </div>
    );
  },
});
